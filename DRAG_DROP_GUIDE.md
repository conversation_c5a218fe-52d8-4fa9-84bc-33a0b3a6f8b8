# 🖱️ 设备拖拽排序功能使用指南

## 功能概述

设备拖拽排序功能允许用户通过鼠标拖拽来重新排列设备卡片的显示顺序，提供更直观的设备管理体验。

## 功能特性

### ✅ 已实现功能
- **拖拽排序**: 支持鼠标拖拽设备卡片重新排序
- **实时更新**: 拖拽完成后立即更新设备显示顺序
- **持久化保存**: 自动保存新的排序到配置文件
- **智能检测**: 自动检测拖拽距离，避免误操作
- **状态反馈**: 提供拖拽过程中的视觉反馈
- **兼容性**: 与现有的设备选择、右键菜单等功能完全兼容

### 🔧 技术实现
- **拖拽协议**: 使用标准的Qt拖拽协议
- **数据传输**: 通过MIME数据传输设备信息
- **事件处理**: 完整的拖拽事件链处理
- **配置管理**: 扩展配置文件格式支持设备顺序

## 使用方法

### 基本拖拽操作

1. **开始拖拽**
   - 将鼠标指针移动到要移动的设备卡片上
   - 按住鼠标左键不放
   - 移动鼠标一小段距离（系统会自动检测拖拽意图）

2. **执行拖拽**
   - 保持按住鼠标左键
   - 将设备卡片拖拽到目标位置
   - 目标设备卡片会高亮显示（接受拖拽状态）

3. **完成拖拽**
   - 在目标设备卡片上释放鼠标左键
   - 系统自动重新排列设备顺序
   - 配置文件自动保存新的排序

### 操作示例

```
原始顺序: [设备A] [设备B] [设备C] [设备D]

拖拽设备C到设备A前面:
1. 点击并拖拽设备C
2. 移动到设备A上方
3. 释放鼠标

结果顺序: [设备C] [设备A] [设备B] [设备D]
```

### 拖拽反馈

- **鼠标指针**: 拖拽时指针会变为移动状态
- **视觉反馈**: 目标区域会显示接受拖拽的状态
- **日志信息**: 操作完成后会在日志中显示排序结果

## 配置文件格式

### 新增字段

拖拽功能在配置文件中新增了 `device_order` 字段：

```json
{
    "devices": [
        {
            "sb_name": "设备A",
            "sb_urls": ["192.168.1.100:5555"],
            "current_url_index": 0,
            "need_open_app": true,
            "is_clone": false,
            "action": "do_job",
            "notes": ""
        }
    ],
    "device_order": ["设备C", "设备A", "设备B", "设备D"]
}
```

### 兼容性

- **向下兼容**: 旧版本配置文件会自动升级
- **自动初始化**: 首次运行时自动生成设备顺序
- **错误恢复**: 配置异常时自动使用默认顺序

## 技术细节

### 拖拽检测

```python
# 拖拽距离检测
if ((event.position().toPoint() - self.drag_start_position).manhattanLength() < 
    QApplication.startDragDistance()):
    return  # 距离不足，不开始拖拽
```

### 数据传输

```python
# MIME数据设置
mime_data = QMimeData()
mime_data.setText(device_name)
mime_data.setData("application/x-device-card", device_name.encode())
```

### 排序算法

```python
def handle_device_reorder(self, source_device: str, target_device: str):
    # 1. 移除源设备
    if source_device in self.device_order:
        self.device_order.remove(source_device)
    
    # 2. 在目标位置插入
    if target_device in self.device_order:
        target_index = self.device_order.index(target_device)
        self.device_order.insert(target_index, source_device)
```

## 故障排除

### 常见问题

1. **拖拽无响应**
   - 检查是否按住了鼠标左键
   - 确认移动距离足够触发拖拽检测
   - 重启程序重新尝试

2. **排序不生效**
   - 检查配置文件是否有写入权限
   - 查看日志中是否有错误信息
   - 确认目标设备存在

3. **配置丢失**
   - 检查配置文件是否损坏
   - 程序会自动重建默认配置
   - 可以从备份恢复配置

### 调试信息

启用详细日志来调试拖拽问题：

```python
# 在DeviceCard类中添加调试信息
def start_drag(self):
    print(f"开始拖拽设备: {self.device_data.get('sb_name', '')}")
    
def dropEvent(self, event):
    source = event.mimeData().data("application/x-device-card").data().decode()
    target = self.device_data.get('sb_name', '')
    print(f"拖拽操作: {source} -> {target}")
```

## 性能优化

### 优化建议

1. **减少重绘**: 拖拽过程中避免频繁重绘界面
2. **批量更新**: 多个拖拽操作可以批量保存配置
3. **内存管理**: 及时清理拖拽过程中的临时对象

### 性能监控

```python
import time

def handle_device_reorder(self, source_device: str, target_device: str):
    start_time = time.time()
    # ... 排序逻辑 ...
    end_time = time.time()
    print(f"排序耗时: {end_time - start_time:.3f}秒")
```

## 扩展功能

### 计划中的增强

1. **拖拽预览**: 显示拖拽过程中的预览效果
2. **批量拖拽**: 支持选中多个设备一起拖拽
3. **分组拖拽**: 支持设备分组和组间拖拽
4. **撤销功能**: 支持撤销拖拽操作
5. **拖拽动画**: 添加平滑的拖拽动画效果

### 自定义配置

```python
# 可配置的拖拽参数
DRAG_CONFIG = {
    'min_drag_distance': 10,  # 最小拖拽距离
    'drag_opacity': 0.7,      # 拖拽时的透明度
    'animation_duration': 200, # 动画持续时间
    'auto_save': True         # 是否自动保存
}
```

## API参考

### 新增方法

```python
class DeviceManager:
    def initialize_device_order(self) -> None:
        """初始化设备顺序"""
        
    def get_ordered_devices(self) -> List[dict]:
        """根据自定义顺序获取设备列表"""
        
    def handle_device_reorder(self, source_device: str, target_device: str) -> None:
        """处理设备重新排序"""

class DeviceCard:
    def start_drag(self) -> None:
        """开始拖拽操作"""
        
    def dragEnterEvent(self, event) -> None:
        """拖拽进入事件"""
        
    def dropEvent(self, event) -> None:
        """拖拽放置事件"""
```

### 配置字段

```python
# 设备顺序配置
device_order: List[str]  # 设备名称的排序列表
```

## 测试验证

### 自动化测试

运行测试脚本验证功能：

```bash
python test_drag_drop.py
```

### 手动测试

1. 启动程序
2. 尝试拖拽不同的设备卡片
3. 验证排序结果
4. 重启程序确认配置持久化

---

**功能版本**: 1.0  
**最后更新**: 2024年12月  
**状态**: ✅ 已完成并测试通过
