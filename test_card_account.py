#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卡账管理功能测试脚本
"""

import json
import os
from datetime import datetime

def test_card_account_data():
    """测试卡账管理数据结构"""
    
    # 测试数据
    test_config = {
        "devices": [
            {
                "sb_name": "测试设备1",
                "sb_urls": ["192.168.1.100:5555"],
                "current_url_index": 0,
                "need_open_app": False,
                "is_clone": False,
                "action": "do_job",
                "notes": "测试设备",
                "adb_connected": True,
                "display_active": False,
                "task_running": False
            }
        ],
        "device_order": ["测试设备1"],
        "phone_cards": {
            "card_001": {
                "phone_number": "***********",
                "device_name": "测试设备1",
                "tags": {
                    "实名": "张三",
                    "话费": "100元"
                },
                "created_time": datetime.now().isoformat(),
                "updated_time": datetime.now().isoformat()
            }
        },
        "app_accounts": {
            "account_001": {
                "app_name": "淘宝",
                "device_name": "测试设备1",
                "app_type": "original",
                "tags": {
                    "实名": "张三",
                    "可充话费": "是"
                },
                "created_time": datetime.now().isoformat(),
                "updated_time": datetime.now().isoformat()
            }
        }
    }
    
    # 保存测试配置
    test_file = "test_device_config.json"
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_config, f, ensure_ascii=False, indent=4)
    
    print(f"测试配置已保存到: {test_file}")
    
    # 验证配置可以正确加载
    with open(test_file, 'r', encoding='utf-8') as f:
        loaded_config = json.load(f)
    
    print("配置加载验证:")
    print(f"设备数量: {len(loaded_config['devices'])}")
    print(f"手机卡数量: {len(loaded_config['phone_cards'])}")
    print(f"APP账号数量: {len(loaded_config['app_accounts'])}")
    
    # 验证数据结构
    device = loaded_config['devices'][0]
    phone_card = list(loaded_config['phone_cards'].values())[0]
    app_account = list(loaded_config['app_accounts'].values())[0]
    
    print(f"\n设备信息: {device['sb_name']}")
    print(f"手机卡: {phone_card['phone_number']} (绑定到: {phone_card['device_name']})")
    print(f"APP账号: {app_account['app_name']} (绑定到: {app_account['device_name']})")
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
        print(f"\n测试文件已清理: {test_file}")
    
    print("\n✅ 卡账管理数据结构测试通过!")

def test_search_functionality():
    """测试搜索功能"""
    print("\n🔍 测试搜索功能...")
    
    # 模拟搜索数据
    devices = [
        {"sb_name": "设备1", "sb_urls": ["192.168.1.100:5555"], "notes": "测试设备"},
        {"sb_name": "设备2", "sb_urls": ["192.168.1.101:5555"], "notes": "生产设备"}
    ]
    
    phone_cards = {
        "card1": {"phone_number": "***********", "device_name": "设备1", "tags": {"实名": "张三"}},
        "card2": {"phone_number": "***********", "device_name": "设备2", "tags": {"实名": "李四"}}
    }
    
    app_accounts = {
        "app1": {"app_name": "淘宝", "device_name": "设备1", "tags": {"实名": "张三"}},
        "app2": {"app_name": "京东", "device_name": "设备2", "tags": {"实名": "李四"}}
    }
    
    def search_devices(search_text):
        """模拟搜索功能"""
        search_text = search_text.lower()
        matched = []
        
        for device in devices:
            device_name = device.get('sb_name', '').lower()
            device_notes = device.get('notes', '').lower()
            
            # 获取关联的卡账信息
            phone_text = ""
            app_text = ""
            
            for card in phone_cards.values():
                if card.get('device_name') == device.get('sb_name'):
                    phone_text += f" {card.get('phone_number', '')}"
                    for tag_name, tag_value in card.get('tags', {}).items():
                        phone_text += f" {tag_name} {tag_value}"
            
            for app in app_accounts.values():
                if app.get('device_name') == device.get('sb_name'):
                    app_text += f" {app.get('app_name', '')}"
                    for tag_name, tag_value in app.get('tags', {}).items():
                        app_text += f" {tag_name} {tag_value}"
            
            phone_text = phone_text.lower()
            app_text = app_text.lower()
            
            if (search_text in device_name or 
                search_text in device_notes or 
                search_text in phone_text or 
                search_text in app_text):
                matched.append(device['sb_name'])
        
        return matched
    
    # 测试各种搜索
    test_cases = [
        ("设备1", ["设备1"]),
        ("张三", ["设备1"]),
        ("淘宝", ["设备1"]),
        ("***********", ["设备1"]),
        ("李四", ["设备2"]),
        ("京东", ["设备2"]),
        ("实名", ["设备1", "设备2"]),
        ("不存在", [])
    ]
    
    for search_term, expected in test_cases:
        result = search_devices(search_term)
        if set(result) == set(expected):
            print(f"✅ 搜索 '{search_term}': {result}")
        else:
            print(f"❌ 搜索 '{search_term}': 期望 {expected}, 实际 {result}")
    
    print("🔍 搜索功能测试完成!")

if __name__ == "__main__":
    print("🚀 开始卡账管理功能测试...")
    
    try:
        test_card_account_data()
        test_search_functionality()
        print("\n🎉 所有测试通过!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
