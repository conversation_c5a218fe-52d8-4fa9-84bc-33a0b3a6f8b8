@echo off
echo 启动设备管理程序 (Flet版本)
echo ================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查是否安装了flet
python -c "import flet" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install flet>=0.21.0 psutil>=5.9.0
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo 启动设备管理程序...
python device_manager_flet.py

if errorlevel 1 (
    echo 程序运行出错
    pause
)
