# 📱 卡账管理功能使用说明

## 功能概述

卡账管理功能是设备管理程序的新增功能，允许您管理手机卡号和APP账号，并将它们与设备进行关联。这个功能可以帮助您更好地组织和管理设备上的账号信息。

## 主要功能

### 1. 手机卡号管理
- ✅ 录入手机号码
- ✅ 绑定到已有设备（下拉框选择）
- ✅ 添加自定义标签（如：实名、话费、运营商等）
- ✅ 自动记录创建和更新时间
- ✅ 支持编辑和删除操作

### 2. APP账号管理
- ✅ 管理APP（淘宝、京东、拼多多等）
- ✅ 绑定到已有设备
- ✅ 选择应用类型（原应用/分身应用）
- ✅ 添加自定义标签（如：实名、可充话费等）
- ✅ 自动记录创建和更新时间
- ✅ 支持编辑和删除操作

### 3. 设备信息增强
- ✅ 在设备信息中显示关联的手机卡号和标签
- ✅ 显示设备上的APP账号和标签
- ✅ 支持复制设备信息到剪贴板

### 4. 搜索功能优化
- ✅ 支持按手机号码搜索
- ✅ 支持按APP名称搜索
- ✅ 支持按标签内容搜索
- ✅ 搜索结果中显示卡账信息

## 使用方法

### 打开卡账管理界面

1. 启动设备管理程序
2. 在菜单栏中选择 **设备** → **📱 卡账管理**（快捷键：Ctrl+M）
3. 卡账管理窗口将打开，包含两个标签页：
   - **📱 手机卡号管理**
   - **📱 APP账号管理**

### 手机卡号管理

#### 添加手机卡
1. 在手机卡号管理标签页中，点击 **➕ 添加手机卡** 按钮
2. 在弹出的对话框中填写：
   - **手机号码**：输入完整的手机号码
   - **绑定设备**：从下拉框中选择要绑定的设备
   - **自定义标签**：添加标签，如：
     - 实名：张三
     - 话费：100元
     - 运营商：中国移动
3. 点击 **保存** 按钮完成添加

#### 编辑手机卡
1. 在手机卡列表中选择要编辑的手机卡
2. 点击 **✏️ 编辑** 按钮
3. 修改相关信息后点击 **保存**

#### 删除手机卡
1. 在手机卡列表中选择要删除的手机卡
2. 点击 **🗑️ 删除** 按钮
3. 确认删除操作

### APP账号管理

#### 添加APP账号
1. 在APP账号管理标签页中，点击 **➕ 添加APP账号** 按钮
2. 在弹出的对话框中填写：
   - **APP名称**：选择或输入APP名称（如：淘宝、京东）
   - **绑定设备**：从下拉框中选择要绑定的设备
   - **应用类型**：选择"原应用"或"分身应用"
   - **自定义标签**：添加标签，如：
     - 实名：张三
     - 可充话费：是
     - 等级：钻石会员
3. 点击 **保存** 按钮完成添加

#### 编辑APP账号
1. 在APP账号列表中选择要编辑的账号
2. 点击 **✏️ 编辑** 按钮
3. 修改相关信息后点击 **保存**

#### 删除APP账号
1. 在APP账号列表中选择要删除的账号
2. 点击 **🗑️ 删除** 按钮
3. 确认删除操作

### 查看设备关联信息

#### 方法一：设备信息对话框
1. 右键点击设备卡片
2. 选择 **📋 设备信息**
3. 在弹出的对话框中查看：
   - 基本设备信息
   - 📱 关联手机卡：显示手机号码和标签
   - 📱 关联APP账号：显示APP名称、类型和标签
4. 可以点击 **📋 复制信息** 按钮将信息复制到剪贴板

#### 方法二：搜索功能
1. 在菜单栏中选择 **设备** → **🔍 搜索信息**（快捷键：Ctrl+F）
2. 在搜索框中输入关键词：
   - 设备名称
   - 手机号码
   - APP名称
   - 标签内容
3. 搜索结果会显示匹配的设备及其卡账信息

## 数据存储

所有卡账管理数据都保存在 `device_config.json` 配置文件中：

```json
{
  "devices": [...],
  "device_order": [...],
  "phone_cards": {
    "card_id": {
      "phone_number": "***********",
      "device_name": "设备名称",
      "tags": {"实名": "张三", "话费": "100元"},
      "created_time": "2025-07-28T18:00:00",
      "updated_time": "2025-07-28T18:00:00"
    }
  },
  "app_accounts": {
    "account_id": {
      "app_name": "淘宝",
      "device_name": "设备名称",
      "app_type": "original",
      "tags": {"实名": "张三", "可充话费": "是"},
      "created_time": "2025-07-28T18:00:00",
      "updated_time": "2025-07-28T18:00:00"
    }
  }
}
```

## 注意事项

1. **数据备份**：建议定期备份 `device_config.json` 文件
2. **设备关联**：删除设备时，相关的手机卡和APP账号不会自动删除
3. **标签管理**：标签名称和值都支持中文，建议使用有意义的标签名
4. **搜索功能**：搜索不区分大小写，支持部分匹配
5. **数据同步**：所有修改会立即保存到配置文件中

## 快捷键

- **Ctrl+M**：打开卡账管理界面
- **Ctrl+F**：打开搜索界面
- **Ctrl+N**：添加新设备

## 故障排除

如果遇到问题，请检查：

1. **配置文件格式**：确保 `device_config.json` 格式正确
2. **权限问题**：确保程序有读写配置文件的权限
3. **设备关联**：确保手机卡和APP账号关联的设备名称存在

---

**版本**：v1.0  
**更新时间**：2025-07-28  
**开发者**：Augment Agent
