#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
拖拽功能调试脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_drag_imports():
    """测试拖拽相关的导入"""
    print("🔍 测试拖拽相关导入...")
    
    try:
        from PyQt6.QtCore import Qt, QMimeData
        print("✅ QtCore 导入成功")
    except ImportError as e:
        print(f"❌ QtCore 导入失败: {e}")
        return False
        
    try:
        from PyQt6.QtGui import QDrag
        print("✅ QDrag 导入成功")
    except ImportError as e:
        print(f"❌ QDrag 导入失败: {e}")
        return False
        
    try:
        from PyQt6.QtWidgets import QApplication
        print("✅ QApplication 导入成功")
    except ImportError as e:
        print(f"❌ QApplication 导入失败: {e}")
        return False
        
    return True

def test_device_card_import():
    """测试设备卡片导入"""
    print("\n🔍 测试设备卡片导入...")
    
    try:
        from device_manager_qt import DeviceCard
        print("✅ DeviceCard 导入成功")
        
        # 检查信号是否存在
        if hasattr(DeviceCard, 'deviceReordered'):
            print("✅ deviceReordered 信号存在")
        else:
            print("❌ deviceReordered 信号不存在")
            return False
            
        return True
    except ImportError as e:
        print(f"❌ DeviceCard 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ DeviceCard 检查失败: {e}")
        return False

def test_drag_distance():
    """测试拖拽距离设置"""
    print("\n🔍 测试拖拽距离设置...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        
        # 创建临时应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
            
        distance = app.startDragDistance()
        print(f"✅ 系统拖拽距离: {distance} 像素")
        
        if distance > 0:
            print("✅ 拖拽距离设置正常")
            return True
        else:
            print("❌ 拖拽距离设置异常")
            return False
            
    except Exception as e:
        print(f"❌ 拖拽距离测试失败: {e}")
        return False

def test_mouse_events():
    """测试鼠标事件方法"""
    print("\n🔍 测试鼠标事件方法...")

    try:
        from PyQt6.QtWidgets import QApplication
        from device_manager_qt import DeviceCard

        # 确保有QApplication实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])

        # 创建测试设备数据
        test_device = {
            'sb_name': 'test_device',
            'sb_urls': ['192.168.1.100:5555'],
            'current_url_index': 0
        }

        # 创建设备卡片实例
        card = DeviceCard(test_device)
        
        # 检查方法是否存在
        methods = ['mousePressEvent', 'mouseMoveEvent', 'start_drag', 
                  'dragEnterEvent', 'dragMoveEvent', 'dropEvent']
        
        for method in methods:
            if hasattr(card, method):
                print(f"✅ {method} 方法存在")
            else:
                print(f"❌ {method} 方法不存在")
                return False
                
        print("✅ 所有鼠标事件方法存在")
        return True
        
    except Exception as e:
        print(f"❌ 鼠标事件测试失败: {e}")
        return False

def test_device_manager_connection():
    """测试设备管理器连接"""
    print("\n🔍 测试设备管理器连接...")
    
    try:
        from device_manager_qt import DeviceManager
        
        # 检查handle_device_reorder方法是否存在
        if hasattr(DeviceManager, 'handle_device_reorder'):
            print("✅ handle_device_reorder 方法存在")
        else:
            print("❌ handle_device_reorder 方法不存在")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 设备管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 拖拽功能调试开始")
    print("=" * 50)
    
    tests = [
        test_drag_imports,
        test_device_card_import,
        test_drag_distance,
        test_mouse_events,
        test_device_manager_connection
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("❌ 测试失败")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！拖拽功能应该可以正常工作")
        print("\n💡 如果拖拽仍然不工作，请检查：")
        print("1. 是否在设备卡片的空白区域拖拽（不要在图标或文字上）")
        print("2. 拖拽距离是否足够（至少移动几个像素）")
        print("3. 是否按住鼠标左键不放")
        print("4. 查看控制台是否有调试输出")
    else:
        print("❌ 部分测试失败，拖拽功能可能有问题")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
