#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的拖拽测试UI
用于验证拖拽功能是否正常工作
"""

import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QFrame)
from PyQt6.QtCore import Qt, pyqtSignal, QMimeData
from PyQt6.QtGui import QDrag

class DragTestCard(QFrame):
    """测试拖拽的简单卡片"""
    
    reordered = pyqtSignal(str, str)
    
    def __init__(self, name, parent=None):
        super().__init__(parent)
        self.name = name
        self.drag_start_position = None
        self.setAcceptDrops(True)
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        self.setFixedSize(120, 80)
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet("""
            QFrame {
                background: lightblue;
                border: 2px solid blue;
                border-radius: 5px;
            }
            QFrame:hover {
                background: lightgreen;
            }
        """)
        
        layout = QVBoxLayout(self)
        label = QLabel(self.name)
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)
        layout.addWidget(label)
        
    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_start_position = event.position().toPoint()
            print(f"[TEST] 鼠标按下: {self.name}")
            
    def mouseMoveEvent(self, event):
        if not (event.buttons() & Qt.MouseButton.LeftButton):
            return
            
        if not self.drag_start_position:
            return
            
        distance = (event.position().toPoint() - self.drag_start_position).manhattanLength()
        if distance < QApplication.startDragDistance():
            return
            
        print(f"[TEST] 开始拖拽: {self.name}")
        self.start_drag()
        
    def start_drag(self):
        drag = QDrag(self)
        mime_data = QMimeData()
        mime_data.setText(self.name)
        mime_data.setData("application/x-test-card", self.name.encode())
        drag.setMimeData(mime_data)
        
        result = drag.exec(Qt.DropAction.MoveAction)
        print(f"[TEST] 拖拽结果: {result}")
        
    def dragEnterEvent(self, event):
        print(f"[TEST] 拖拽进入: {self.name}")
        if event.mimeData().hasFormat("application/x-test-card"):
            event.acceptProposedAction()
        else:
            event.ignore()
            
    def dragMoveEvent(self, event):
        if event.mimeData().hasFormat("application/x-test-card"):
            event.acceptProposedAction()
        else:
            event.ignore()
            
    def dropEvent(self, event):
        print(f"[TEST] 拖拽放置到: {self.name}")
        if event.mimeData().hasFormat("application/x-test-card"):
            source = event.mimeData().data("application/x-test-card").data().decode()
            print(f"[TEST] 拖拽操作: {source} -> {self.name}")
            self.reordered.emit(source, self.name)
            event.acceptProposedAction()
        else:
            event.ignore()

class DragTestWindow(QMainWindow):
    """拖拽测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("拖拽功能测试")
        self.setGeometry(100, 100, 600, 200)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 说明标签
        info_label = QLabel("拖拽测试：尝试拖拽下面的卡片来重新排序")
        info_label.setStyleSheet("font-size: 14px; padding: 10px;")
        layout.addWidget(info_label)
        
        # 卡片容器
        cards_widget = QWidget()
        self.cards_layout = QHBoxLayout(cards_widget)
        layout.addWidget(cards_widget)
        
        # 创建测试卡片
        self.cards = []
        for i in range(4):
            card = DragTestCard(f"卡片{i+1}")
            card.reordered.connect(self.handle_reorder)
            self.cards.append(card)
            self.cards_layout.addWidget(card)
            
        # 结果显示
        self.result_label = QLabel("拖拽结果将显示在这里")
        self.result_label.setStyleSheet("font-size: 12px; padding: 10px; background: #f0f0f0;")
        layout.addWidget(self.result_label)
        
    def handle_reorder(self, source, target):
        """处理重新排序"""
        message = f"拖拽操作: {source} -> {target}"
        print(f"[TEST] {message}")
        self.result_label.setText(message)
        
        # 模拟重新排序
        source_index = -1
        target_index = -1
        
        for i, card in enumerate(self.cards):
            if card.name == source:
                source_index = i
            if card.name == target:
                target_index = i
                
        if source_index != -1 and target_index != -1:
            # 移动卡片
            card = self.cards.pop(source_index)
            self.cards.insert(target_index, card)
            
            # 重新布局
            for i in reversed(range(self.cards_layout.count())):
                item = self.cards_layout.itemAt(i)
                if item:
                    widget = item.widget()
                    if widget:
                        self.cards_layout.removeWidget(widget)
                        
            for card in self.cards:
                self.cards_layout.addWidget(card)
                
            print(f"[TEST] 新顺序: {[card.name for card in self.cards]}")

def main():
    app = QApplication(sys.argv)
    
    window = DragTestWindow()
    window.show()
    
    print("拖拽测试程序启动")
    print("请尝试拖拽卡片来测试拖拽功能")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
