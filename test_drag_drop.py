#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
设备拖拽排序功能测试脚本
"""

import sys
import json
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt6.QtCore import Qt

def test_device_order_functionality():
    """测试设备顺序功能"""
    print("🧪 开始测试设备拖拽排序功能...")
    
    # 测试配置文件路径
    test_config_file = "test_devices_config.json"
    
    # 创建测试配置
    test_config = {
        "devices": [
            {"sb_name": "设备A", "sb_urls": ["192.168.1.100:5555"], "current_url_index": 0},
            {"sb_name": "设备B", "sb_urls": ["192.168.1.101:5555"], "current_url_index": 0},
            {"sb_name": "设备C", "sb_urls": ["192.168.1.102:5555"], "current_url_index": 0},
            {"sb_name": "设备D", "sb_urls": ["192.168.1.103:5555"], "current_url_index": 0},
        ],
        "device_order": ["设备A", "设备B", "设备C", "设备D"]
    }
    
    # 保存测试配置
    with open(test_config_file, 'w', encoding='utf-8') as f:
        json.dump(test_config, f, ensure_ascii=False, indent=4)
    
    print("✅ 测试配置文件已创建")
    
    # 导入设备管理器
    try:
        from device_manager_qt import DeviceManager
        print("✅ 设备管理器模块导入成功")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建设备管理器实例
    manager = DeviceManager()
    manager.config_file = test_config_file  # 使用测试配置文件
    
    # 测试配置加载
    print("🔄 测试配置加载...")
    manager.load_config()
    
    print(f"📋 加载的设备数量: {len(manager.devices)}")
    print(f"📋 设备顺序: {manager.device_order}")
    
    # 测试设备顺序获取
    print("🔄 测试设备顺序获取...")
    ordered_devices = manager.get_ordered_devices()
    ordered_names = [device.get('sb_name', '') for device in ordered_devices]
    print(f"📋 排序后的设备: {ordered_names}")
    
    # 测试设备重新排序
    print("🔄 测试设备重新排序...")
    print("原始顺序:", manager.device_order)
    
    # 模拟将"设备C"移动到"设备A"前面
    manager.handle_device_reorder("设备C", "设备A")
    print("移动设备C到设备A前面后:", manager.device_order)
    
    # 验证排序结果
    expected_order = ["设备C", "设备A", "设备B", "设备D"]
    if manager.device_order == expected_order:
        print("✅ 设备重新排序测试通过")
    else:
        print(f"❌ 设备重新排序测试失败，期望: {expected_order}, 实际: {manager.device_order}")
    
    # 测试配置保存和加载
    print("🔄 测试配置保存和重新加载...")
    manager.save_config()
    
    # 重新加载配置验证持久化
    manager.device_order = []  # 清空
    manager.load_config()
    
    if manager.device_order == expected_order:
        print("✅ 配置持久化测试通过")
    else:
        print(f"❌ 配置持久化测试失败，期望: {expected_order}, 实际: {manager.device_order}")
    
    # 测试添加新设备
    print("🔄 测试添加新设备...")
    new_device = {"sb_name": "新设备", "sb_urls": ["192.168.1.104:5555"], "current_url_index": 0}
    manager.devices.append(new_device)
    manager.device_order.append("新设备")
    
    ordered_devices = manager.get_ordered_devices()
    ordered_names = [device.get('sb_name', '') for device in ordered_devices]
    expected_with_new = ["设备C", "设备A", "设备B", "设备D", "新设备"]
    
    if ordered_names == expected_with_new:
        print("✅ 添加新设备测试通过")
    else:
        print(f"❌ 添加新设备测试失败，期望: {expected_with_new}, 实际: {ordered_names}")
    
    # 测试删除设备
    print("🔄 测试删除设备...")
    # 模拟删除"设备B"
    manager.devices = [d for d in manager.devices if d.get('sb_name') != '设备B']
    if '设备B' in manager.device_order:
        manager.device_order.remove('设备B')
    
    ordered_devices = manager.get_ordered_devices()
    ordered_names = [device.get('sb_name', '') for device in ordered_devices]
    expected_after_delete = ["设备C", "设备A", "设备D", "新设备"]
    
    if ordered_names == expected_after_delete:
        print("✅ 删除设备测试通过")
    else:
        print(f"❌ 删除设备测试失败，期望: {expected_after_delete}, 实际: {ordered_names}")
    
    # 清理测试文件
    try:
        os.remove(test_config_file)
        print("🧹 测试配置文件已清理")
    except:
        pass
    
    print("🎉 设备拖拽排序功能测试完成！")
    return True

def test_drag_drop_ui():
    """测试拖拽UI功能"""
    print("\n🖱️ 开始测试拖拽UI功能...")
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = QMainWindow()
    window.setWindowTitle("拖拽功能测试")
    window.setGeometry(100, 100, 800, 600)
    
    central_widget = QWidget()
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("""
    拖拽功能测试说明：
    
    1. 程序已启动，您可以看到设备卡片
    2. 尝试按住鼠标左键拖拽设备卡片
    3. 将设备拖拽到另一个设备卡片上释放
    4. 观察设备顺序是否发生变化
    5. 检查日志中是否有排序成功的消息
    
    注意：
    - 拖拽时鼠标指针应该变化
    - 放置时应该看到设备重新排列
    - 配置文件会自动保存新的顺序
    """)
    info_label.setWordWrap(True)
    info_label.setStyleSheet("""
        QLabel {
            background: #f0f0f0;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 10px;
            font-size: 12px;
        }
    """)
    
    layout.addWidget(info_label)
    window.setCentralWidget(central_widget)
    
    print("✅ 测试UI窗口已创建")
    print("💡 请在主程序窗口中测试拖拽功能")
    print("💡 测试完成后关闭此窗口")
    
    window.show()
    
    # 不执行事件循环，让主程序处理
    return True

if __name__ == "__main__":
    print("🚀 设备拖拽排序功能测试")
    print("=" * 50)
    
    # 测试核心功能
    success = test_device_order_functionality()
    
    if success:
        print("\n" + "=" * 50)
        print("✅ 所有核心功能测试通过！")
        print("\n现在可以在主程序中测试拖拽UI功能：")
        print("1. 运行 python device_manager_qt.py")
        print("2. 尝试拖拽设备卡片")
        print("3. 观察设备顺序变化")
        
        # 显示测试UI
        if len(sys.argv) > 1 and sys.argv[1] == "--ui":
            test_drag_drop_ui()
    else:
        print("\n❌ 测试失败，请检查代码实现")
        sys.exit(1)
