#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件迁移脚本 - 将旧格式转换为新的卡账管理格式
"""

import json
import os
import shutil
from datetime import datetime

def migrate_config():
    """迁移配置文件"""
    config_file = "device_config.json"
    backup_file = "device_config_backup.json"
    
    if not os.path.exists(config_file):
        print("配置文件不存在，无需迁移")
        return
    
    # 备份原配置文件
    shutil.copy2(config_file, backup_file)
    print(f"已备份原配置文件到: {backup_file}")
    
    # 加载原配置
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 检查是否已经是新格式
    if 'phone_cards' in config and 'app_accounts' in config:
        print("配置文件已经是新格式，无需迁移")
        return
    
    # 初始化新字段
    if 'phone_cards' not in config:
        config['phone_cards'] = {}
    
    if 'app_accounts' not in config:
        config['app_accounts'] = {}
    
    # 清理设备中的旧标签数据
    for device in config.get('devices', []):
        # 移除旧的标签字段
        if 'tags' in device:
            del device['tags']
        if 'tag_update_times' in device:
            del device['tag_update_times']
    
    # 保存迁移后的配置
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=4)
    
    print("配置文件迁移完成!")
    print(f"设备数量: {len(config.get('devices', []))}")
    print(f"手机卡数量: {len(config.get('phone_cards', {}))}")
    print(f"APP账号数量: {len(config.get('app_accounts', {}))}")

def add_sample_data():
    """添加示例卡账数据"""
    config_file = "device_config.json"
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 获取第一个设备作为示例
    devices = config.get('devices', [])
    if not devices:
        print("没有设备，无法添加示例数据")
        return
    
    first_device = devices[0]['sb_name']
    current_time = datetime.now().isoformat()
    
    # 添加示例手机卡
    config['phone_cards']['sample_card_001'] = {
        "phone_number": "***********",
        "device_name": first_device,
        "tags": {
            "实名": "张三",
            "话费": "100元",
            "运营商": "中国移动"
        },
        "created_time": current_time,
        "updated_time": current_time
    }
    
    # 添加示例APP账号
    config['app_accounts']['sample_app_001'] = {
        "app_name": "淘宝",
        "device_name": first_device,
        "app_type": "original",
        "tags": {
            "实名": "张三",
            "可充话费": "是",
            "等级": "钻石会员"
        },
        "created_time": current_time,
        "updated_time": current_time
    }
    
    config['app_accounts']['sample_app_002'] = {
        "app_name": "京东",
        "device_name": first_device,
        "app_type": "clone",
        "tags": {
            "实名": "李四",
            "可充话费": "否"
        },
        "created_time": current_time,
        "updated_time": current_time
    }
    
    # 保存配置
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=4)
    
    print(f"已为设备 '{first_device}' 添加示例数据:")
    print("- 1个手机卡: ***********")
    print("- 2个APP账号: 淘宝(原应用), 京东(分身应用)")

if __name__ == "__main__":
    print("🔄 开始配置文件迁移...")
    
    try:
        migrate_config()
        
        # 询问是否添加示例数据
        response = input("\n是否添加示例卡账数据？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            add_sample_data()
        
        print("\n✅ 迁移完成!")
        
    except Exception as e:
        print(f"\n❌ 迁移失败: {str(e)}")
        import traceback
        traceback.print_exc()
