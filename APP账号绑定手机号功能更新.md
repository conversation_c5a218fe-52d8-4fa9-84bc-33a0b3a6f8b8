# 📱 APP账号绑定手机号功能更新

## 更新内容

根据您的需求，我已经为APP账号管理功能添加了"绑定手机号码"字段。现在在编辑APP账号时，可以选择绑定已有的手机卡号。

## 新增功能

### 1. APP账号编辑界面更新

在"编辑APP账号"对话框中，新增了"绑定手机号码"字段：

- **位置**：在"APP名称"和"绑定设备"之间
- **功能**：下拉框选择已有的手机卡号
- **显示格式**：`手机号码 (绑定设备)`
- **默认选项**：未绑定

### 2. APP账号列表显示更新

APP账号管理表格新增了"绑定手机号"列：

- **原列顺序**：APP名称 | 绑定设备 | 应用类型 | 标签 | 更新时间 | 操作
- **新列顺序**：APP名称 | **绑定手机号** | 绑定设备 | 应用类型 | 标签 | 更新时间 | 操作

### 3. 设备信息显示增强

在查看设备信息时，APP账号信息现在包含手机号：

```
📱 关联APP账号：
  • 淘宝 [手机号:***********] (原应用) - 实名:张三, 可充话费:是
  • 京东 [无手机号] (分身应用) - 实名:李四, 可充话费:否
```

### 4. 搜索功能增强

搜索功能现在支持按APP账号的手机号码搜索：

- 可以搜索手机号码找到相关的APP账号
- 搜索结果显示APP账号的手机号信息

## 数据结构更新

APP账号数据结构新增`phone_number`字段：

```json
{
  "app_accounts": {
    "account_id": {
      "app_name": "淘宝",
      "phone_number": "***********",  // 新增字段
      "device_name": "设备名称",
      "app_type": "original",
      "tags": {"实名": "张三"},
      "created_time": "2025-07-28T18:00:00",
      "updated_time": "2025-07-28T18:00:00"
    }
  }
}
```

## 使用方法

### 为APP账号绑定手机号

1. 打开卡账管理界面（设备菜单 → 卡账管理）
2. 切换到"APP账号管理"标签页
3. 选择要编辑的APP账号，点击"编辑"按钮
4. 在"绑定手机号码"下拉框中选择要绑定的手机号
5. 点击"保存"完成绑定

### 添加新APP账号时绑定手机号

1. 在APP账号管理页面点击"添加APP账号"
2. 填写APP名称
3. 在"绑定手机号码"下拉框中选择手机号（可选）
4. 选择绑定设备和应用类型
5. 添加自定义标签
6. 点击"保存"完成添加

## 注意事项

1. **手机号码来源**：下拉框中的手机号码来自已添加的手机卡
2. **可选绑定**：APP账号可以不绑定手机号码
3. **显示格式**：下拉框显示格式为"手机号码 (绑定设备)"
4. **数据兼容性**：现有的APP账号数据会自动添加空的phone_number字段
5. **搜索功能**：可以通过手机号码搜索相关的APP账号

## 界面截图说明

根据您提供的截图，新的界面布局如下：

```
┌─────────────────────────────────────┐
│ 📱 编辑APP账号                      │
├─────────────────────────────────────┤
│ APP名称:     [淘宝            ▼]    │
│ 绑定手机号码: [未绑定          ▼]    │  ← 新增字段
│ 绑定设备:     [10x            ▼]    │
│ 应用类型:     [原应用          ▼]    │
│                                     │
│ 自定义标签:                         │
│ ┌─────────────────────────────────┐ │
│ │ [实名    ] [南（未）] [🗑️]      │ │
│ └─────────────────────────────────┘ │
│                                     │
│ [➕ 添加标签]                       │
│                                     │
│           [取消]     [保存]         │
└─────────────────────────────────────┘
```

## 技术实现

1. **UI组件**：在`AppAccountEditDialog`类中添加了`phone_combo`下拉框
2. **数据加载**：`load_phone_numbers()`方法从手机卡数据中加载选项
3. **数据保存**：`save_account()`方法保存选中的手机号码
4. **表格显示**：APP账号表格增加了手机号码列
5. **搜索增强**：搜索功能包含手机号码字段

## 版本信息

- **更新版本**：v1.1
- **更新时间**：2025-07-28
- **更新内容**：APP账号绑定手机号功能
- **兼容性**：向下兼容现有数据

---

**开发者**：Augment Agent  
**更新完成**：✅ 功能已实现并测试通过
