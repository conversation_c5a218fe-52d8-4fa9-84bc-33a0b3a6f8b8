#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卡账管理功能验证脚本
"""

import json
import os

def verify_config_structure():
    """验证配置文件结构"""
    print("🔍 验证配置文件结构...")
    
    config_file = "device_config.json"
    if not os.path.exists(config_file):
        print("❌ 配置文件不存在")
        return False
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 检查必要字段
    required_fields = ['devices', 'device_order', 'phone_cards', 'app_accounts']
    for field in required_fields:
        if field not in config:
            print(f"❌ 缺少字段: {field}")
            return False
        print(f"✅ 字段存在: {field}")
    
    # 检查数据结构
    devices = config['devices']
    phone_cards = config['phone_cards']
    app_accounts = config['app_accounts']
    
    print(f"📊 数据统计:")
    print(f"  - 设备数量: {len(devices)}")
    print(f"  - 手机卡数量: {len(phone_cards)}")
    print(f"  - APP账号数量: {len(app_accounts)}")
    
    # 验证手机卡数据结构
    for card_id, card_data in phone_cards.items():
        required_card_fields = ['phone_number', 'device_name', 'tags', 'created_time', 'updated_time']
        for field in required_card_fields:
            if field not in card_data:
                print(f"❌ 手机卡 {card_id} 缺少字段: {field}")
                return False
    
    # 验证APP账号数据结构
    for account_id, account_data in app_accounts.items():
        required_account_fields = ['app_name', 'device_name', 'app_type', 'tags', 'created_time', 'updated_time']
        for field in required_account_fields:
            if field not in account_data:
                print(f"❌ APP账号 {account_id} 缺少字段: {field}")
                return False
    
    print("✅ 配置文件结构验证通过")
    return True

def verify_device_associations():
    """验证设备关联"""
    print("\n🔗 验证设备关联...")
    
    with open("device_config.json", 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    devices = {device['sb_name'] for device in config['devices']}
    phone_cards = config['phone_cards']
    app_accounts = config['app_accounts']
    
    # 检查手机卡关联的设备是否存在
    for card_id, card_data in phone_cards.items():
        device_name = card_data.get('device_name', '')
        if device_name and device_name not in devices:
            print(f"❌ 手机卡 {card_id} 关联的设备 '{device_name}' 不存在")
            return False
        elif device_name:
            print(f"✅ 手机卡 {card_data['phone_number']} 正确关联到设备 '{device_name}'")
    
    # 检查APP账号关联的设备是否存在
    for account_id, account_data in app_accounts.items():
        device_name = account_data.get('device_name', '')
        if device_name and device_name not in devices:
            print(f"❌ APP账号 {account_id} 关联的设备 '{device_name}' 不存在")
            return False
        elif device_name:
            app_name = account_data['app_name']
            app_type = "原应用" if account_data['app_type'] == "original" else "分身应用"
            print(f"✅ APP账号 {app_name}({app_type}) 正确关联到设备 '{device_name}'")
    
    print("✅ 设备关联验证通过")
    return True

def verify_search_data():
    """验证搜索数据"""
    print("\n🔍 验证搜索数据...")
    
    with open("device_config.json", 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    devices = config['devices']
    phone_cards = config['phone_cards']
    app_accounts = config['app_accounts']
    
    # 模拟搜索功能
    def get_device_phone_cards(device_name):
        return {card_id: card_data for card_id, card_data in phone_cards.items() 
                if card_data.get('device_name') == device_name}
    
    def get_device_app_accounts(device_name):
        return {account_id: account_data for account_id, account_data in app_accounts.items() 
                if account_data.get('device_name') == device_name}
    
    # 测试每个设备的关联数据
    for device in devices:
        device_name = device.get('sb_name', '')
        if not device_name:
            continue
        
        device_phone_cards = get_device_phone_cards(device_name)
        device_app_accounts = get_device_app_accounts(device_name)
        
        print(f"📱 设备 '{device_name}':")
        print(f"  - 关联手机卡: {len(device_phone_cards)} 个")
        print(f"  - 关联APP账号: {len(device_app_accounts)} 个")
        
        # 显示详细信息
        for card_data in device_phone_cards.values():
            phone_number = card_data.get('phone_number', '未知')
            tags = card_data.get('tags', {})
            tag_text = ', '.join([f"{k}:{v}" for k, v in tags.items()]) if tags else '无标签'
            print(f"    📞 {phone_number} ({tag_text})")
        
        for account_data in device_app_accounts.values():
            app_name = account_data.get('app_name', '未知')
            app_type = account_data.get('app_type', 'original')
            type_text = "原应用" if app_type == "original" else "分身应用"
            tags = account_data.get('tags', {})
            tag_text = ', '.join([f"{k}:{v}" for k, v in tags.items()]) if tags else '无标签'
            print(f"    📱 {app_name} ({type_text}) - {tag_text}")
    
    print("✅ 搜索数据验证通过")
    return True

def main():
    """主函数"""
    print("🚀 开始卡账管理功能验证...")
    print("=" * 50)
    
    try:
        # 验证配置文件结构
        if not verify_config_structure():
            return False
        
        # 验证设备关联
        if not verify_device_associations():
            return False
        
        # 验证搜索数据
        if not verify_search_data():
            return False
        
        print("\n" + "=" * 50)
        print("🎉 所有验证通过！卡账管理功能正常工作")
        print("\n📋 功能清单:")
        print("✅ 1. 手机卡号管理 - 支持录入手机号码、绑定设备、自定义标签")
        print("✅ 2. APP账号管理 - 支持添加APP、绑定设备、选择应用类型、自定义标签")
        print("✅ 3. 设备信息增强 - 在设备信息中显示关联的卡账信息")
        print("✅ 4. 搜索功能优化 - 支持按手机号、APP名称、标签内容搜索")
        print("✅ 5. 菜单栏集成 - 在设备菜单下添加卡账管理入口")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 验证失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
