# 🔧 技术实现细节

## 架构概览

设备管理程序采用模块化设计，主要由以下几个核心组件构成：

### 核心组件
- **DeviceManager**: 主窗口类，负责整体协调
- **DeviceCard**: 设备卡片组件，支持拖拽排序
- **WindowManager**: 窗口管理模块（实时检测）
- **ConfigManager**: 配置管理模块
- **ADBManager**: ADB连接管理模块

## 🖥️ 窗口管理系统

### 实时检测机制
```python
def get_all_scrcpy_windows(self):
    """获取所有scrcpy窗口 - 使用Windows API实时检测"""
    windows = []
    
    def enum_windows_proc(hwnd, lParam):
        if user32.IsWindowVisible(hwnd):
            title = self.get_window_title(hwnd)
            # 检测scrcpy窗口的多种模式
            if self.is_scrcpy_window(title):
                windows.append({
                    'hwnd': hwnd,
                    'title': title,
                    'pid': self.get_window_pid(hwnd)
                })
        return True
    
    user32.EnumWindows(enum_windows_proc, 0)
    return windows
```

### 窗口操作API
- **查找窗口**: `find_window_by_pid_simple()`
- **关闭窗口**: `close_window()`
- **排列窗口**: `arrange_windows()`
- **置顶窗口**: `bring_device_window_to_front()`

### 设备显示状态管理
```python
def has_display_running(self, device_name: str) -> bool:
    """实时检测设备显示状态"""
    scrcpy_windows = self.get_all_scrcpy_windows()
    return any(device_name in window['title'] for window in scrcpy_windows)
```

## 🎯 拖拽排序系统

### 拖拽事件处理链
1. **mousePressEvent**: 记录拖拽起始位置
2. **mouseMoveEvent**: 检测拖拽距离，触发拖拽
3. **start_drag**: 创建拖拽对象和数据
4. **dragEnterEvent**: 验证拖拽数据
5. **dropEvent**: 处理拖拽放置，发射信号

### 智能排序算法
```python
def handle_device_reorder(self, source_device: str, target_device: str):
    """智能设备重新排序"""
    source_index = self.device_order.index(source_device)
    target_index = self.device_order.index(target_device)
    
    # 根据原始位置关系决定插入位置
    insert_after = source_index < target_index
    
    self.device_order.remove(source_device)
    target_index = self.device_order.index(target_device)
    
    if insert_after:
        # 从左拖到右：插入到目标后面
        insert_position = target_index + 1
    else:
        # 从右拖到左：插入到目标前面
        insert_position = target_index
    
    self.device_order.insert(insert_position, source_device)
```

### 事件穿透优化
```python
# 确保子组件不拦截鼠标事件
self.icon_label.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)
self.name_label.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)
self.top_widget.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)
```

## 🔗 ADB连接管理

### 连接状态检测
```python
def check_device_connection(self, device):
    """检查单个设备的连接状态"""
    for i, url in enumerate(device['sb_urls']):
        try:
            result = subprocess.run(
                ['adb', 'connect', url],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if 'connected' in result.stdout:
                device['current_url_index'] = i
                return True
        except subprocess.TimeoutExpired:
            continue
    
    return False
```

### 自动重连机制
- **重试次数**: 最多3次
- **重试间隔**: 递增延迟
- **失败处理**: 标记设备为离线状态

## 💾 配置管理系统

### 配置文件结构
```json
{
    "devices": [
        {
            "sb_name": "设备名称",
            "sb_urls": ["IP:端口"],
            "current_url_index": 0,
            "sb_remark": "备注信息",
            "display_active": false
        }
    ],
    "device_order": ["设备1", "设备2", "设备3"],
    "version": "2.0"
}
```

### 版本兼容性
- **自动升级**: 检测旧版本格式并自动转换
- **向下兼容**: 保持对旧版本的支持
- **错误恢复**: 配置损坏时的重建机制

## 🎨 UI组件系统

### 设备卡片设计
```python
class DeviceCard(QFrame):
    # 信号定义
    clicked = pyqtSignal(str)
    doubleClicked = pyqtSignal(str)
    rightClicked = pyqtSignal(str, QPoint)
    deviceReordered = pyqtSignal(str, str)  # 拖拽排序信号
```

### 状态可视化
- **连接状态**: 绿色(已连接) / 红色(未连接) / 灰色(未知)
- **任务状态**: 蓝色(运行中) / 透明(空闲)
- **显示状态**: 黄色边框(显示中) / 无边框(未显示)

### 响应式布局
- **自适应宽度**: 根据窗口大小调整卡片数量
- **固定高度**: 保持卡片比例一致
- **动态排列**: 支持拖拽重新排序

## 🔍 搜索和过滤

### 实时搜索算法
```python
def filter_devices(self, search_text):
    """实时过滤设备"""
    if not search_text:
        return self.devices
    
    filtered = []
    for device in self.devices:
        if (search_text.lower() in device['sb_name'].lower() or
            search_text.lower() in device.get('sb_remark', '').lower() or
            any(search_text in url for url in device['sb_urls'])):
            filtered.append(device)
    
    return filtered
```

### 搜索范围
- **设备名称**: 模糊匹配
- **IP地址**: 精确匹配
- **备注信息**: 模糊匹配

## 📊 性能优化

### 内存管理
- **按需加载**: 只在需要时创建UI组件
- **及时释放**: 不再使用的对象立即释放
- **缓存策略**: 合理使用缓存减少重复计算

### 响应速度优化
- **异步操作**: 耗时操作使用线程池
- **批量处理**: 合并相似操作减少系统调用
- **实时更新**: 避免全量刷新，只更新变化部分

### 资源使用优化
- **图标缓存**: 复用相同的图标资源
- **字体优化**: 使用系统字体减少资源占用
- **内存监控**: 定期检查内存使用情况

## 🔒 错误处理机制

### 异常捕获策略
```python
try:
    # 核心操作
    result = risky_operation()
except SpecificException as e:
    # 特定异常处理
    self.log(f"特定错误: {str(e)}")
    self.handle_specific_error(e)
except Exception as e:
    # 通用异常处理
    self.log(f"未知错误: {str(e)}")
    self.handle_generic_error(e)
finally:
    # 清理资源
    self.cleanup_resources()
```

### 错误恢复机制
- **自动重试**: 网络相关操作的自动重试
- **状态重置**: 错误后的状态恢复
- **用户提示**: 友好的错误信息显示

## 🧪 测试框架

### 单元测试
- **功能测试**: 核心功能的单元测试
- **边界测试**: 边界条件的测试用例
- **异常测试**: 异常情况的处理测试

### 集成测试
- **端到端测试**: 完整流程的集成测试
- **兼容性测试**: 不同环境下的兼容性
- **性能测试**: 性能指标的基准测试

## 📈 监控和日志

### 日志系统
```python
def log(self, message: str, level: str = "INFO"):
    """统一日志记录"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] {message}"
    
    # 控制台输出
    print(log_entry)
    
    # UI显示
    self.log_text.append(log_entry)
    
    # 文件记录（可选）
    if self.enable_file_logging:
        self.write_to_log_file(log_entry)
```

### 性能监控
- **响应时间**: 关键操作的响应时间监控
- **资源使用**: CPU和内存使用情况
- **错误率**: 操作失败率统计

---

**文档版本**: 1.0  
**最后更新**: 2024年12月  
**技术栈**: Python 3.8+, PyQt6, Windows API
