# 📱 卡账管理界面优化更新

## 更新概述

根据您的需求，我对卡账管理功能进行了三项重要优化：

1. **手机卡号管理**：将"操作"列改为显示"关联APP"信息
2. **APP账号管理**：添加手机卡号和设备筛选功能
3. **关联编辑**：添加"编辑关联APP"按钮，实现跨标签页跳转和筛选

## 详细更新内容

### 1. 手机卡号管理界面优化

#### 更改内容
- **原来**：最后一列显示"操作"（编辑/删除按钮）
- **现在**：最后一列显示"关联APP"信息

#### 新功能
- **关联APP显示**：自动显示该手机号关联的所有APP
- **显示格式**：`APP名称(类型), APP名称(类型)...`
- **示例**：`淘宝(原), 京东(分身)`
- **无关联时**：显示"无关联APP"

#### 新增按钮
- **📱 编辑关联APP**：点击后跳转到APP账号管理标签页
- **按钮状态**：选中手机卡时才启用
- **功能**：自动筛选显示该手机号的APP账号

### 2. APP账号管理筛选功能

#### 新增筛选区域
在APP账号管理标签页的按钮区域下方添加了筛选控件：

```
[➕ 添加APP账号] [✏️ 编辑] [🗑️ 删除] [🔄 刷新]

手机号筛选: [全部 ▼]  设备筛选: [全部 ▼]  [🔄 清除筛选]
```

#### 筛选功能
- **手机号筛选**：下拉框显示所有已有的手机号码
- **设备筛选**：下拉框显示所有已有的设备名称
- **实时筛选**：选择筛选条件后立即更新表格显示
- **清除筛选**：一键清除所有筛选条件

### 3. 跨标签页关联编辑

#### 工作流程
1. 在手机卡号管理中选择一个手机卡
2. 点击"📱 编辑关联APP"按钮
3. 自动切换到APP账号管理标签页
4. 自动设置手机号筛选为选中的手机号
5. 只显示该手机号关联的APP账号

#### 用户体验
- **无缝切换**：自动跳转，无需手动操作
- **智能筛选**：自动设置筛选条件
- **专注编辑**：只显示相关的APP账号
- **日志记录**：操作会记录到日志中

## 界面对比

### 手机卡号管理（更新前后）

**更新前：**
```
| 手机号码      | 绑定设备 | 标签        | 更新时间     | 操作     |
|--------------|---------|-------------|-------------|----------|
| ***********  | 10x     | 实名:张三   | 2025-07-28  | 编辑/删除 |
```

**更新后：**
```
| 手机号码      | 绑定设备 | 标签        | 更新时间     | 关联APP        |
|--------------|---------|-------------|-------------|---------------|
| ***********  | 10x     | 实名:张三   | 2025-07-28  | 淘宝(原), 京东(分身) |

[➕ 添加手机卡] [✏️ 编辑] [🗑️ 删除] [📱 编辑关联APP] [🔄 刷新]
```

### APP账号管理（新增筛选）

**更新后：**
```
[➕ 添加APP账号] [✏️ 编辑] [🗑️ 删除] [🔄 刷新]

手机号筛选: [*********** ▼]  设备筛选: [10x ▼]  [🔄 清除筛选]

| APP名称 | 绑定手机号   | 绑定设备 | 应用类型 | 标签      | 更新时间   | 操作     |
|---------|-------------|---------|----------|-----------|-----------|----------|
| 淘宝    | *********** | 10x     | 原应用   | 实名:张三 | 2025-07-28| 编辑/删除 |
```

## 技术实现

### 1. 关联APP显示
```python
def get_related_apps(self, phone_number):
    """获取手机号关联的APP列表"""
    related_apps = []
    for account_data in self.main_window.app_accounts.values():
        if account_data.get('phone_number') == phone_number:
            app_name = account_data.get('app_name', '未知')
            app_type = "原" if account_data.get('app_type') == "original" else "分身"
            related_apps.append(f"{app_name}({app_type})")
    return related_apps
```

### 2. 筛选功能
```python
def apply_filters(self):
    """应用筛选条件"""
    phone_filter = self.phone_filter_combo.currentData()
    device_filter = self.device_filter_combo.currentData()
    # 根据筛选条件过滤数据并更新表格
```

### 3. 跨标签页跳转
```python
def edit_related_apps(self):
    """编辑关联APP"""
    # 获取选中的手机号
    # 切换到APP账号管理标签页
    # 设置手机号筛选条件
    main_dialog.tab_widget.setCurrentWidget(main_dialog.app_account_tab)
    main_dialog.app_account_tab.set_phone_filter(phone_number)
```

## 使用说明

### 查看手机卡关联的APP
1. 打开卡账管理界面
2. 在手机卡号管理标签页中查看"关联APP"列
3. 可以直观看到每个手机号关联了哪些APP

### 编辑手机卡的关联APP
1. 在手机卡号管理中选择要编辑的手机卡
2. 点击"📱 编辑关联APP"按钮
3. 自动跳转到APP账号管理，并筛选显示该手机号的APP
4. 在此页面可以添加、编辑、删除该手机号的APP账号

### 使用筛选功能
1. 在APP账号管理标签页中
2. 使用"手机号筛选"查看特定手机号的APP
3. 使用"设备筛选"查看特定设备的APP
4. 可以同时使用两个筛选条件
5. 点击"清除筛选"恢复显示所有APP

## 注意事项

1. **数据同步**：关联APP信息实时更新，无需手动刷新
2. **筛选状态**：筛选条件在标签页切换时会保持
3. **按钮状态**：编辑关联APP按钮只在选中手机卡时启用
4. **兼容性**：所有更新向下兼容现有数据

## 版本信息

- **更新版本**：v1.2
- **更新时间**：2025-07-28
- **更新内容**：界面优化和筛选功能
- **兼容性**：完全向下兼容

---

**开发者**：Augment Agent  
**状态**：✅ 所有功能已实现并测试通过
