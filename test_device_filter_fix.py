#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设备筛选修复功能
"""

import sys
import json
import os
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QTimer

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_device_filter():
    """测试设备筛选功能"""
    
    # 创建测试数据
    test_devices = [
        {
            'sb_name': 'mix2_lan',
            'sb_urls': ['192.168.123.224:5555'],
            'notes': '手机卡1: *********** 实名: 南'
        },
        {
            'sb_name': 'test_device',
            'sb_urls': ['192.168.1.100:5555'],
            'notes': '测试设备'
        }
    ]
    
    test_phone_cards = {
        'card_001': {
            'phone_number': '***********',
            'device_name': 'mix2_lan',
            'tags': {'实名': '南', '运营商': '移动'}
        },
        'card_002': {
            'phone_number': '***********',
            'device_name': 'mix2_lan',
            'tags': {'实名': '微', '话费': '50'}
        },
        'card_003': {
            'phone_number': '***********',
            'device_name': 'test_device',
            'tags': {'实名': '测试'}
        }
    }
    
    test_app_accounts = {
        'app_001': {
            'app_name': '淘宝',
            'phone_number': '***********',
            'device_name': 'mix2_lan',
            'app_type': 'original',
            'tags': {'实名': '南'}
        },
        'app_002': {
            'app_name': '京东',
            'phone_number': '***********',
            'device_name': 'mix2_lan',
            'app_type': 'clone',
            'tags': {'实名': '微'}
        },
        'app_003': {
            'app_name': '拼多多',
            'phone_number': '***********',
            'device_name': 'test_device',
            'app_type': 'original',
            'tags': {'实名': '测试'}
        }
    }
    
    # 保存测试数据到配置文件
    config_data = {
        'devices': test_devices,
        'phone_cards': test_phone_cards,
        'app_accounts': test_app_accounts
    }
    
    with open('device_config.json', 'w', encoding='utf-8') as f:
        json.dump(config_data, f, ensure_ascii=False, indent=2)
    
    print("测试数据已保存到 device_config.json")
    print("\n测试步骤：")
    print("1. 启动设备管理程序")
    print("2. 右键点击 'mix2_lan' 设备，选择 '编辑设备'")
    print("3. 在设备编辑对话框中点击 '📱 编辑手机卡' 按钮")
    print("4. 检查卡账管理对话框是否正确切换到手机卡管理标签页")
    print("5. 检查是否正确筛选显示 'mix2_lan' 设备的手机卡（应该显示2张卡）")
    print("6. 返回设备编辑对话框，点击 '📱 编辑账号信息' 按钮")
    print("7. 检查是否正确切换到APP账号管理标签页")
    print("8. 检查是否正确筛选显示 'mix2_lan' 设备的APP账号（应该显示2个账号）")
    print("\n预期结果：")
    print("- 手机卡管理应该只显示 mix2_lan 设备的2张手机卡")
    print("- APP账号管理应该只显示 mix2_lan 设备的2个APP账号")
    print("- 程序日志中应该显示筛选操作的详细信息")

def simulate_filter_test():
    """模拟筛选测试"""
    print("\n=== 模拟筛选测试 ===")
    
    # 模拟手机卡数据
    phone_cards = {
        'card_001': {'phone_number': '***********', 'device_name': 'mix2_lan'},
        'card_002': {'phone_number': '***********', 'device_name': 'mix2_lan'},
        'card_003': {'phone_number': '***********', 'device_name': 'test_device'}
    }
    
    # 模拟APP账号数据
    app_accounts = {
        'app_001': {'app_name': '淘宝', 'device_name': 'mix2_lan'},
        'app_002': {'app_name': '京东', 'device_name': 'mix2_lan'},
        'app_003': {'app_name': '拼多多', 'device_name': 'test_device'}
    }
    
    def filter_by_device(data, device_name):
        """按设备筛选数据"""
        filtered = {}
        for item_id, item_data in data.items():
            if item_data.get('device_name', '') == device_name:
                filtered[item_id] = item_data
        return filtered
    
    # 测试筛选 mix2_lan 设备
    device_name = 'mix2_lan'
    filtered_cards = filter_by_device(phone_cards, device_name)
    filtered_accounts = filter_by_device(app_accounts, device_name)
    
    print(f"筛选设备: {device_name}")
    print(f"手机卡总数: {len(phone_cards)}, 筛选后: {len(filtered_cards)}")
    print(f"APP账号总数: {len(app_accounts)}, 筛选后: {len(filtered_accounts)}")
    
    print("\n筛选后的手机卡:")
    for card_id, card_data in filtered_cards.items():
        print(f"  {card_id}: {card_data['phone_number']}")
    
    print("\n筛选后的APP账号:")
    for app_id, app_data in filtered_accounts.items():
        print(f"  {app_id}: {app_data['app_name']}")
    
    # 验证筛选结果
    expected_cards = 2
    expected_accounts = 2
    
    if len(filtered_cards) == expected_cards and len(filtered_accounts) == expected_accounts:
        print(f"\n✅ 筛选测试通过！")
    else:
        print(f"\n❌ 筛选测试失败！")
        print(f"期望手机卡数: {expected_cards}, 实际: {len(filtered_cards)}")
        print(f"期望APP账号数: {expected_accounts}, 实际: {len(filtered_accounts)}")

if __name__ == '__main__':
    print("设备筛选修复测试")
    print("=" * 50)
    
    # 运行模拟测试
    simulate_filter_test()
    
    # 准备实际测试数据
    test_device_filter()
    
    print("\n" + "=" * 50)
    print("测试准备完成！请启动设备管理程序进行实际测试。")
