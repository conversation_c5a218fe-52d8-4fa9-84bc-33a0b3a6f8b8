#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试设备重新排序逻辑
"""

def test_reorder_logic():
    """测试重新排序逻辑"""
    print("🧪 测试设备重新排序逻辑")
    print("=" * 50)
    
    def simulate_reorder(device_order, source_device, target_device):
        """模拟设备重新排序"""
        print(f"\n📋 原始顺序: {device_order}")
        print(f"🔄 操作: 将 '{source_device}' 拖拽到 '{target_device}' 上")
        
        if source_device == target_device:
            print("⚠️ 源设备和目标设备相同，无需操作")
            return device_order
        
        if source_device not in device_order or target_device not in device_order:
            print("❌ 源设备或目标设备不存在")
            return device_order
        
        # 获取位置
        source_index = device_order.index(source_device)
        target_index = device_order.index(target_device)
        
        print(f"📍 源设备位置: {source_index}, 目标设备位置: {target_index}")
        
        # 移除源设备
        new_order = device_order.copy()
        new_order.remove(source_device)
        
        # 重新获取目标设备位置
        target_index = new_order.index(target_device)
        
        # 插入到目标设备后面
        insert_position = target_index + 1
        new_order.insert(insert_position, source_device)
        
        print(f"✅ 新的顺序: {new_order}")
        print(f"💡 结果: '{source_device}' 现在在 '{target_device}' 后面")
        
        return new_order
    
    # 测试用例
    test_cases = [
        {
            "name": "从左拖到右",
            "initial": ["A", "B", "C", "D"],
            "source": "A",
            "target": "C",
            "expected": ["B", "C", "A", "D"]
        },
        {
            "name": "从右拖到左", 
            "initial": ["A", "B", "C", "D"],
            "source": "D",
            "target": "B",
            "expected": ["A", "B", "D", "C"]
        },
        {
            "name": "相邻设备拖拽",
            "initial": ["A", "B", "C", "D"],
            "source": "B",
            "target": "C",
            "expected": ["A", "C", "B", "D"]
        },
        {
            "name": "拖拽到最后",
            "initial": ["A", "B", "C", "D"],
            "source": "A",
            "target": "D",
            "expected": ["B", "C", "D", "A"]
        },
        {
            "name": "拖拽到第一个",
            "initial": ["A", "B", "C", "D"],
            "source": "D",
            "target": "A",
            "expected": ["A", "D", "B", "C"]
        }
    ]
    
    passed = 0
    total = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试 {i}: {test_case['name']}")
        print("-" * 30)
        
        result = simulate_reorder(
            test_case["initial"].copy(),
            test_case["source"],
            test_case["target"]
        )
        
        if result == test_case["expected"]:
            print("✅ 测试通过")
            passed += 1
        else:
            print(f"❌ 测试失败")
            print(f"   期望: {test_case['expected']}")
            print(f"   实际: {result}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！排序逻辑正确")
    else:
        print("❌ 部分测试失败，需要修复排序逻辑")
    
    return passed == total

def test_edge_cases():
    """测试边界情况"""
    print("\n🔍 测试边界情况")
    print("=" * 30)
    
    def simulate_reorder_safe(device_order, source_device, target_device):
        """安全的重新排序模拟"""
        try:
            if source_device == target_device:
                return device_order, "源设备和目标设备相同"
            
            if source_device not in device_order:
                return device_order, f"源设备 '{source_device}' 不存在"
                
            if target_device not in device_order:
                return device_order, f"目标设备 '{target_device}' 不存在"
            
            new_order = device_order.copy()
            new_order.remove(source_device)
            target_index = new_order.index(target_device)
            new_order.insert(target_index + 1, source_device)
            
            return new_order, "成功"
        except Exception as e:
            return device_order, f"异常: {str(e)}"
    
    edge_cases = [
        {
            "name": "空列表",
            "initial": [],
            "source": "A",
            "target": "B"
        },
        {
            "name": "单个设备",
            "initial": ["A"],
            "source": "A",
            "target": "A"
        },
        {
            "name": "源设备不存在",
            "initial": ["A", "B", "C"],
            "source": "X",
            "target": "B"
        },
        {
            "name": "目标设备不存在",
            "initial": ["A", "B", "C"],
            "source": "A",
            "target": "X"
        },
        {
            "name": "两个设备",
            "initial": ["A", "B"],
            "source": "A",
            "target": "B"
        }
    ]
    
    for case in edge_cases:
        print(f"\n🧪 {case['name']}")
        result, message = simulate_reorder_safe(
            case["initial"].copy(),
            case["source"],
            case["target"]
        )
        print(f"   原始: {case['initial']}")
        print(f"   操作: {case['source']} -> {case['target']}")
        print(f"   结果: {result}")
        print(f"   状态: {message}")

if __name__ == "__main__":
    success = test_reorder_logic()
    test_edge_cases()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 排序逻辑测试完成，可以在主程序中测试拖拽功能")
        print("\n💡 测试建议:")
        print("1. 尝试从左边拖拽设备到右边")
        print("2. 尝试从右边拖拽设备到左边") 
        print("3. 观察设备是否出现在目标设备的后面")
        print("4. 检查日志中的详细排序信息")
    else:
        print("❌ 排序逻辑有问题，需要进一步修复")
