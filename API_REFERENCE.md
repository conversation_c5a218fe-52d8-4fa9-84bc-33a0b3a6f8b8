# 📚 API参考文档

## 概述

本文档描述了设备管理程序的主要类、方法和接口。适用于需要扩展或修改程序功能的开发者。

## 核心类结构

### DeviceManager (主窗口类)

主要的应用程序窗口类，继承自 `QMainWindow`。

#### 初始化
```python
class DeviceManager(QMainWindow):
    def __init__(self):
        super().__init__()
        self.devices = []                    # 设备列表
        self.selected_devices = set()        # 选中的设备
        self.device_cards = {}              # 设备卡片字典
        self.running_processes = {}         # 运行中的进程
        self.scrcpy_processes = {}          # scrcpy进程
        self.device_logs = {}               # 设备日志
```

#### 主要方法

##### 设备管理
```python
def add_device(self) -> None:
    """添加新设备"""
    
def edit_device(self, device: dict) -> None:
    """编辑设备信息"""
    
def delete_device(self, device: dict) -> None:
    """删除设备"""
    
def show_device_info(self, device: dict) -> None:
    """显示设备详细信息"""
```

##### ADB连接管理
```python
def refresh_adb_status(self) -> None:
    """刷新ADB连接状态"""
    
def try_connect_device(self, device_url: str, device_name: str) -> bool:
    """尝试连接指定设备"""
    
def update_all_device_status(self) -> None:
    """更新所有设备状态"""
```

##### 任务执行
```python
def run_device(self, device_name: str) -> None:
    """运行指定设备任务"""
    
def stop_device(self, device_name: str) -> None:
    """停止指定设备任务"""
    
def generate_command(self, device: dict) -> str:
    """生成执行命令"""
    
def monitor_device_output(self, device_name: str, process: subprocess.Popen) -> None:
    """监控设备进程输出"""
```

##### 显示管理
```python
def open_device_display(self, device_name: str) -> None:
    """打开设备显示"""
    
def close_device_display(self, device_name: str) -> None:
    """关闭设备显示"""
    
def generate_scrcpy_command(self, device: dict) -> str:
    """生成scrcpy命令"""
    
def bring_device_window_to_front(self, device_name: str) -> bool:
    """将设备窗口置于前台"""
```

##### 批量操作
```python
def run_selected_devices(self) -> None:
    """运行选中的设备"""
    
def stop_selected_devices(self) -> None:
    """停止选中的设备"""
    
def open_selected_displays(self) -> None:
    """打开选中设备的显示"""
    
def close_selected_displays(self) -> None:
    """关闭选中设备的显示"""
```

### DeviceCard (设备卡片类)

设备卡片UI组件，继承自 `QFrame`。

#### 信号定义
```python
class DeviceCard(QFrame):
    # 信号定义
    clicked = pyqtSignal(str)           # 设备名称
    doubleClicked = pyqtSignal(str)     # 双击信号
    rightClicked = pyqtSignal(str, QPoint)  # 右键信号
```

#### 主要方法
```python
def __init__(self, device_data: dict, parent=None, card_height=160):
    """初始化设备卡片"""
    
def update_data(self, device_data: dict) -> None:
    """更新设备数据"""
    
def update_device_icon(self) -> None:
    """根据设备状态更新图标"""
    
def set_selected(self, selected: bool) -> None:
    """设置选中状态"""
    
def get_current_url(self) -> str:
    """获取当前URL"""
```

### RunTaskDialog (运行任务对话框)

任务运行配置对话框，继承自 `QDialog`。

#### 主要方法
```python
def __init__(self, device_data: dict = None, parent=None):
    """初始化对话框"""
    
def get_run_config(self) -> dict:
    """获取运行配置"""
    
def setup_multi_device_info(self, device_list: list) -> None:
    """设置多设备信息显示"""
```

### DeviceEditDialog (设备编辑对话框)

设备编辑对话框，继承自 `QDialog`。

#### 主要方法
```python
def __init__(self, device_data: dict = None, parent=None):
    """初始化对话框"""
    
def get_data(self) -> dict:
    """获取表单数据"""
    
def load_data(self) -> None:
    """加载设备数据到表单"""
```

### ClosableTabWidget (可关闭标签页)

支持双击关闭的标签页组件，继承自 `QTabWidget`。

#### 主要方法
```python
def on_tab_double_clicked(self, index: int) -> None:
    """处理标签页双击事件"""
    
def close_tab(self, index: int, tab_text: str) -> None:
    """关闭指定的标签页"""
```

## 数据结构

### 设备数据结构
```python
device_data = {
    'sb_name': str,              # 设备名称
    'sb_urls': List[str],        # 设备URL列表
    'current_url_index': int,    # 当前URL索引
    'need_open_app': bool,       # 是否需要开启应用
    'is_clone': bool,            # 是否启用分身模式
    'action': str,               # 执行动作
    'notes': str,                # 备注信息
    'adb_connected': bool,       # ADB连接状态
    'display_active': bool,      # 显示状态
    'task_running': bool,        # 任务运行状态
}
```

### 配置文件结构
```python
config_data = {
    'devices': List[device_data]  # 设备列表
}
```

### 运行配置结构
```python
run_config = {
    'need_open_app': bool,       # 启动时开启应用
    'is_clone': bool,            # 启用分身模式
    'action': str                # 执行动作
}
```

## 事件系统

### 设备卡片事件
```python
# 单击事件
def on_device_clicked(self, device_name: str) -> None:
    """处理设备单击事件"""

# 双击事件  
def on_device_double_clicked(self, device_name: str) -> None:
    """处理设备双击事件"""

# 右键事件
def on_device_right_clicked(self, device_name: str, pos: QPoint) -> None:
    """处理设备右键事件"""
```

### 日志事件
```python
class LogSignals(QObject):
    log_message = pyqtSignal(str, str)  # message, device_name

def log_from_thread(self, message: str, device_name: str = "") -> None:
    """从子线程安全地记录日志"""
```

## 工具函数

### Windows API相关
```python
def get_all_scrcpy_windows(self) -> List[dict]:
    """获取所有scrcpy窗口"""
    
def bring_window_to_front(self, hwnd) -> bool:
    """将窗口置于前台"""
    
def close_window(self, window_info) -> bool:
    """关闭单个窗口"""
    
def arrange_window(self, window_info, index: int, layout_info: dict) -> bool:
    """排列单个窗口"""
```

### 配置管理
```python
def load_config(self) -> None:
    """加载配置文件"""
    
def save_config(self) -> bool:
    """保存配置文件"""
    
def import_config(self) -> None:
    """导入配置"""
    
def export_config(self) -> None:
    """导出配置"""
```

## 扩展接口

### 自定义设备类型
要添加新的设备类型支持，需要：

1. 修改 `generate_scrcpy_command` 方法
2. 添加设备类型检测逻辑
3. 配置相应的scrcpy参数

```python
def generate_scrcpy_command(self, device: dict) -> str:
    device_name = device['sb_name']
    device_url = self.get_device_url(device)
    
    # 添加新设备类型检测
    if 'new_device_type' in device_name.lower():
        command = f'"{scrcpy_path}" -s {device_url} --new-params'
    else:
        # 现有逻辑
        pass
    
    return command
```

### 自定义任务动作
要添加新的任务动作，需要：

1. 在 `RunTaskDialog` 中添加新选项
2. 修改 `generate_command` 方法
3. 确保对应的执行脚本存在

```python
# 在RunTaskDialog.__init__中
self.action_combo.addItems([
    "do_job", "test", "monitor", "open_tmall", "new_action"
])

# 在generate_command中处理新动作
def generate_command(self, device: dict) -> str:
    action = device.get('action')
    if action == 'new_action':
        cmd_parts.extend(['--action', '"new_action"'])
    # 其他逻辑...
```

### 插件接口（规划中）
```python
class PluginInterface:
    """插件接口基类"""
    
    def get_name(self) -> str:
        """获取插件名称"""
        raise NotImplementedError
    
    def get_version(self) -> str:
        """获取插件版本"""
        raise NotImplementedError
    
    def initialize(self, main_window) -> None:
        """初始化插件"""
        raise NotImplementedError
    
    def cleanup(self) -> None:
        """清理插件资源"""
        raise NotImplementedError
```

## 错误处理

### 异常类型
程序中主要的异常处理：

```python
try:
    # 设备操作
except subprocess.TimeoutExpired:
    # 超时处理
except subprocess.CalledProcessError:
    # 进程错误处理
except ConnectionError:
    # 连接错误处理
except Exception as e:
    # 通用错误处理
    self.log(f"操作失败: {str(e)}")
```

### 日志级别（规划中）
```python
class LogLevel:
    DEBUG = 0
    INFO = 1
    WARNING = 2
    ERROR = 3
    CRITICAL = 4
```

## 性能考虑

### 线程安全
- 使用 `pyqtSignal` 进行线程间通信
- UI更新必须在主线程中进行
- 长时间操作在后台线程执行

### 内存管理
- 及时清理已结束的进程
- 限制日志条目数量
- 使用 `deleteLater()` 清理UI组件

### 性能优化
- 避免频繁的状态更新
- 使用缓存减少重复计算
- 合理设置定时器间隔

---

**文档版本**: 1.0  
**适用程序版本**: 1.0.0  
**最后更新**: 2024年12月
