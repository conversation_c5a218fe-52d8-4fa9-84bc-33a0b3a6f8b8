@echo off
echo 启动设备管理程序 - PyQt6版本
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

REM 检查PyQt6是否安装
python -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

REM 启动程序
echo 启动设备管理程序...
python device_manager_qt.py

if errorlevel 1 (
    echo.
    echo 程序运行出错，请检查错误信息
    pause
)
