#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试智能重新排序逻辑
"""

def test_smart_reorder_logic():
    """测试智能重新排序逻辑"""
    print("🧪 测试智能设备重新排序逻辑")
    print("=" * 50)
    
    def simulate_smart_reorder(device_order, source_device, target_device):
        """模拟智能设备重新排序"""
        print(f"\n📋 原始顺序: {device_order}")
        print(f"🔄 操作: 将 '{source_device}' 拖拽到 '{target_device}' 上")
        
        if source_device == target_device:
            print("⚠️ 源设备和目标设备相同，无需操作")
            return device_order
        
        if source_device not in device_order or target_device not in device_order:
            print("❌ 源设备或目标设备不存在")
            return device_order
        
        # 获取原始位置
        source_index = device_order.index(source_device)
        target_index = device_order.index(target_device)
        
        print(f"📍 源设备位置: {source_index}, 目标设备位置: {target_index}")
        
        # 判断插入位置的逻辑
        # 如果源设备在目标设备的右边（索引更大），插入到目标设备前面
        # 如果源设备在目标设备的左边（索引更小），插入到目标设备后面
        insert_after = source_index < target_index
        
        # 移除源设备
        new_order = device_order.copy()
        new_order.remove(source_device)
        
        # 重新获取目标设备位置
        target_index = new_order.index(target_device)
        
        # 根据原始位置关系决定插入位置
        if insert_after:
            # 源设备原本在左边，插入到目标设备后面
            insert_position = target_index + 1
            position_desc = "后面"
        else:
            # 源设备原本在右边，插入到目标设备前面
            insert_position = target_index
            position_desc = "前面"
        
        new_order.insert(insert_position, source_device)
        
        print(f"✅ 新的顺序: {new_order}")
        print(f"💡 结果: '{source_device}' 现在在 '{target_device}' {position_desc}")
        
        return new_order
    
    # 测试用例
    test_cases = [
        {
            "name": "从左拖到右（插入后面）",
            "initial": ["A", "B", "C", "D"],
            "source": "A",
            "target": "C",
            "expected": ["B", "C", "A", "D"],
            "description": "A在C左边，所以插入到C后面"
        },
        {
            "name": "从右拖到左（插入前面）", 
            "initial": ["A", "B", "C", "D"],
            "source": "D",
            "target": "B",
            "expected": ["A", "D", "B", "C"],
            "description": "D在B右边，所以插入到B前面"
        },
        {
            "name": "相邻设备：右拖左",
            "initial": ["A", "B", "C", "D"],
            "source": "C",
            "target": "B",
            "expected": ["A", "C", "B", "D"],
            "description": "C在B右边，所以插入到B前面"
        },
        {
            "name": "相邻设备：左拖右",
            "initial": ["A", "B", "C", "D"],
            "source": "B",
            "target": "C",
            "expected": ["A", "C", "B", "D"],
            "description": "B在C左边，所以插入到C后面"
        },
        {
            "name": "拖拽到最后",
            "initial": ["A", "B", "C", "D"],
            "source": "A",
            "target": "D",
            "expected": ["B", "C", "D", "A"],
            "description": "A在D左边，所以插入到D后面"
        },
        {
            "name": "从最后拖到第一个",
            "initial": ["A", "B", "C", "D"],
            "source": "D",
            "target": "A",
            "expected": ["D", "A", "B", "C"],
            "description": "D在A右边，所以插入到A前面"
        }
    ]
    
    passed = 0
    total = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试 {i}: {test_case['name']}")
        print(f"📝 说明: {test_case['description']}")
        print("-" * 40)
        
        result = simulate_smart_reorder(
            test_case["initial"].copy(),
            test_case["source"],
            test_case["target"]
        )
        
        if result == test_case["expected"]:
            print("✅ 测试通过")
            passed += 1
        else:
            print(f"❌ 测试失败")
            print(f"   期望: {test_case['expected']}")
            print(f"   实际: {result}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！智能排序逻辑正确")
    else:
        print("❌ 部分测试失败，需要修复排序逻辑")
    
    return passed == total

def test_real_scenario():
    """测试真实场景"""
    print("\n🎯 测试真实场景")
    print("=" * 30)
    
    # 模拟您遇到的实际情况
    initial_order = ['10x', 'mix2_hong', 'mix2_lan', 'mix2_lv', 'zte', 'mix2_zi', 'mix2_bai', 'mix2_hei', 'mix_fen', 'redmi_note3', 'k80pro']
    
    print(f"初始顺序: {initial_order}")
    print(f"mix2_lv 位置: {initial_order.index('mix2_lv')} (索引3)")
    print(f"zte 位置: {initial_order.index('zte')} (索引4)")
    
    def smart_reorder(order, source, target):
        source_idx = order.index(source)
        target_idx = order.index(target)
        insert_after = source_idx < target_idx
        
        new_order = order.copy()
        new_order.remove(source)
        target_idx = new_order.index(target)
        
        if insert_after:
            new_order.insert(target_idx + 1, source)
            pos = "后面"
        else:
            new_order.insert(target_idx, source)
            pos = "前面"
            
        return new_order, pos
    
    # 第一次拖拽：mix2_lv 拖到 zte
    print(f"\n🔄 第一次拖拽: mix2_lv -> zte")
    result1, pos1 = smart_reorder(initial_order, 'mix2_lv', 'zte')
    print(f"结果: mix2_lv 在 zte {pos1}")
    print(f"新顺序: {result1}")
    
    # 第二次拖拽：mix2_lv 再次拖到 zte
    print(f"\n🔄 第二次拖拽: mix2_lv -> zte (从新位置)")
    result2, pos2 = smart_reorder(result1, 'mix2_lv', 'zte')
    print(f"结果: mix2_lv 在 zte {pos2}")
    print(f"最终顺序: {result2}")
    
    print(f"\n💡 分析:")
    print(f"第一次: mix2_lv(3) < zte(4) → 插入到zte后面")
    print(f"第二次: mix2_lv(4) > zte(3) → 插入到zte前面")
    print(f"这样就实现了来回切换的效果！")

if __name__ == "__main__":
    success = test_smart_reorder_logic()
    test_real_scenario()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 智能排序逻辑测试完成！")
        print("\n💡 新逻辑的优势:")
        print("1. 从左拖到右：插入到目标后面")
        print("2. 从右拖到左：插入到目标前面")
        print("3. 相邻设备可以来回切换位置")
        print("4. 符合用户的直觉操作")
    else:
        print("❌ 智能排序逻辑有问题，需要进一步修复")
