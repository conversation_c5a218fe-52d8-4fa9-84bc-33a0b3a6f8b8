#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI更新功能
"""

import json
import os

def test_related_apps_display():
    """测试关联APP显示格式"""
    print("🔍 测试关联APP显示格式...")
    
    # 模拟数据
    app_accounts = {
        "app1": {
            "app_name": "淘宝",
            "phone_number": "***********",
            "device_name": "10x",
            "app_type": "original"
        },
        "app2": {
            "app_name": "京东",
            "phone_number": "***********", 
            "device_name": "mix2_lan",
            "app_type": "clone"
        },
        "app3": {
            "app_name": "拼多多",
            "phone_number": "***********",
            "device_name": "10x",
            "app_type": "original"
        }
    }
    
    def get_related_apps(phone_number):
        """模拟获取关联APP的方法"""
        related_apps = []
        for account_data in app_accounts.values():
            if account_data.get('phone_number') == phone_number:
                app_name = account_data.get('app_name', '未知')
                app_type = account_data.get('app_type', 'original')
                device_name = account_data.get('device_name', '未知设备')
                type_text = "原" if app_type == "original" else "分身"
                related_apps.append(f"{app_name}({type_text}) - {device_name}")
        return related_apps
    
    # 测试不同手机号的关联APP显示
    test_cases = [
        ("***********", ["淘宝(原) - 10x", "京东(分身) - mix2_lan"]),
        ("***********", ["拼多多(原) - 10x"]),
        ("***********", [])
    ]
    
    for phone_number, expected in test_cases:
        result = get_related_apps(phone_number)
        expected_text = ', '.join(expected) if expected else '无关联APP'
        result_text = ', '.join(result) if result else '无关联APP'
        
        if result_text == expected_text:
            print(f"✅ 手机号 {phone_number}: {result_text}")
        else:
            print(f"❌ 手机号 {phone_number}: 期望 '{expected_text}', 实际 '{result_text}'")
    
    print("✅ 关联APP显示格式测试完成")

def test_filter_functionality():
    """测试筛选功能"""
    print("\n🔍 测试筛选功能...")
    
    # 模拟APP账号数据
    app_accounts = {
        "app1": {
            "app_name": "淘宝",
            "phone_number": "***********",
            "device_name": "10x",
            "app_type": "original"
        },
        "app2": {
            "app_name": "京东", 
            "phone_number": "***********",
            "device_name": "mix2_lan",
            "app_type": "clone"
        },
        "app3": {
            "app_name": "拼多多",
            "phone_number": "***********",
            "device_name": "10x",
            "app_type": "original"
        },
        "app4": {
            "app_name": "抖音",
            "phone_number": "",
            "device_name": "mix2_lan",
            "app_type": "original"
        }
    }
    
    def apply_filters(phone_filter="", device_filter=""):
        """模拟筛选功能"""
        filtered_accounts = {}
        for account_id, account_data in app_accounts.items():
            # 手机号筛选
            if phone_filter and account_data.get('phone_number', '') != phone_filter:
                continue
            # 设备筛选
            if device_filter and account_data.get('device_name', '') != device_filter:
                continue
            filtered_accounts[account_id] = account_data
        return filtered_accounts
    
    # 测试筛选功能
    test_cases = [
        ("", "", 4, "无筛选"),
        ("***********", "", 2, "按手机号筛选"),
        ("", "10x", 2, "按设备筛选"),
        ("***********", "10x", 1, "组合筛选"),
        ("***********", "", 0, "不存在的手机号"),
        ("", "不存在设备", 0, "不存在的设备")
    ]
    
    for phone_filter, device_filter, expected_count, description in test_cases:
        result = apply_filters(phone_filter, device_filter)
        actual_count = len(result)
        
        if actual_count == expected_count:
            print(f"✅ {description}: 筛选出 {actual_count} 个APP账号")
        else:
            print(f"❌ {description}: 期望 {expected_count} 个, 实际 {actual_count} 个")
            
        # 显示筛选结果详情
        if actual_count > 0:
            apps = [data['app_name'] for data in result.values()]
            print(f"   筛选结果: {', '.join(apps)}")
    
    print("✅ 筛选功能测试完成")

def test_config_compatibility():
    """测试配置文件兼容性"""
    print("\n🔍 测试配置文件兼容性...")
    
    config_file = "device_config.json"
    if not os.path.exists(config_file):
        print("❌ 配置文件不存在")
        return
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 检查APP账号是否有phone_number字段
    app_accounts = config.get('app_accounts', {})
    missing_phone_field = 0
    
    for account_id, account_data in app_accounts.items():
        if 'phone_number' not in account_data:
            missing_phone_field += 1
    
    if missing_phone_field == 0:
        print(f"✅ 所有 {len(app_accounts)} 个APP账号都有phone_number字段")
    else:
        print(f"❌ 有 {missing_phone_field} 个APP账号缺少phone_number字段")
    
    # 检查数据结构
    required_fields = ['devices', 'device_order', 'phone_cards', 'app_accounts']
    for field in required_fields:
        if field in config:
            print(f"✅ 配置文件包含 {field} 字段")
        else:
            print(f"❌ 配置文件缺少 {field} 字段")
    
    print("✅ 配置文件兼容性测试完成")

def main():
    """主函数"""
    print("🚀 开始UI更新功能测试...")
    print("=" * 50)
    
    try:
        test_related_apps_display()
        test_filter_functionality()
        test_config_compatibility()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试完成！")
        print("\n📋 更新内容:")
        print("✅ 1. 关联APP显示格式: 应用(类型) - 设备")
        print("✅ 2. APP账号管理筛选功能: 手机号 + 设备筛选")
        print("✅ 3. 编辑关联APP按钮: 跨标签页跳转和筛选")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
