# 📖 设备管理程序用户手册

## 目录
1. [快速开始](#快速开始)
2. [界面介绍](#界面介绍)
3. [设备管理](#设备管理)
4. [任务执行](#任务执行)
5. [显示管理](#显示管理)
6. [批量操作](#批量操作)
7. [日志查看](#日志查看)
8. [设置配置](#设置配置)
9. [常见问题](#常见问题)
10. [快捷键参考](#快捷键参考)

## 快速开始

### 环境准备
1. **安装Python依赖**
   ```bash
   pip install PyQt6 psutil
   ```

2. **安装ADB工具**
   - 下载Android SDK Platform Tools
   - 将ADB添加到系统PATH环境变量

3. **安装scrcpy**
   - 下载scrcpy并解压到指定目录
   - 确保程序中的scrcpy路径正确

### 首次运行
1. 运行程序：`python device_manager_qt.py`
2. 程序会自动创建示例设备配置
3. 连接您的Android设备并启用USB调试
4. 点击"🔄 刷新ADB"按钮检测设备连接

## 界面介绍

### 主界面布局
```
┌─────────────────────────────────────────────────────┐
│ 菜单栏: 文件 | 设备 | 设置 | 帮助                      │
├─────────────────────────────────────────────────────┤
│ 工具栏: [🔄 刷新ADB] [⏹️ 全部停止] [❌ 关闭所有显示]    │
├─────────────────────────────────────────────────────┤
│                                                     │
│  设备网格区域                                        │
│  ┌─────┐ ┌─────┐ ┌─────┐                           │
│  │设备1│ │设备2│ │设备3│                           │
│  └─────┘ └─────┘ └─────┘                           │
│                                                     │
├─────────────────────────────────────────────────────┤
│ 日志区域                                             │
│ [程序日志] [设备1] [设备2] ...                        │
│ 日志内容显示区域                                      │
└─────────────────────────────────────────────────────┘
```

### 设备卡片说明
每个设备卡片显示：
- **设备图标**: 根据状态显示不同图标
  - 📱 空闲状态
  - 🖥️ 显示运行中
  - ⚙️ 任务执行中
- **设备名称**: 显示在卡片底部
- **状态颜色**: 图标背景色表示连接状态
  - 彩色：ADB已连接
  - 灰色：ADB未连接

## 设备管理

### 添加新设备
1. **方法一**: 菜单栏 → 设备 → ➕ 添加设备
2. **方法二**: 快捷键 `Ctrl+N`

**填写设备信息**:
- **设备名称**: 给设备起一个便于识别的名称
- **设备URL**: 设备的ADB连接地址，格式：`IP:端口`
  ```
  示例：
  *************:5555
  *************:5555
  ```
- **备注**: 可选的设备描述信息

### 编辑设备
1. 右键点击设备卡片
2. 选择"✏️ 编辑设备"
3. 修改设备信息并保存

### 删除设备
1. 右键点击设备卡片
2. 选择"🗑️ 删除设备"
3. 确认删除操作

**注意**: 删除设备会自动停止相关任务和关闭显示

### 查看设备信息
1. 右键点击设备卡片
2. 选择"📋 设备信息"
3. 查看详细的设备状态和配置

## 任务执行

### 运行单个设备任务
1. 右键点击设备卡片
2. 选择"▶️ 运行任务"
3. 在弹出的配置对话框中设置：
   - **启动时开启应用**: 是否在任务开始时启动应用
   - **启用分身模式**: 是否使用分身模式运行
   - **执行动作**: 选择要执行的动作类型
     - `do_job`: 执行主要任务
     - `test`: 测试模式
     - `monitor`: 监控模式
     - `open_tmall`: 打开天猫应用
4. 点击"开始运行"

### 停止任务
1. 右键点击正在运行的设备
2. 选择"⏹️ 停止任务"

### 任务状态监控
- 设备卡片图标会显示当前状态
- 查看对应的设备日志标签页了解详细执行情况

## 显示管理

### 打开设备显示
**方法一**: 双击设备卡片
**方法二**: 右键 → "🖥️ 打开显示"

### 显示窗口操作
- **置于前台**: 双击已有显示的设备卡片
- **关闭显示**: 右键 → "❌ 关闭显示"
- **窗口排列**: 工具栏 → "🔽 并列窗口"

### scrcpy参数说明
程序会根据设备类型自动选择参数：
- **普通设备**: `-Sw -b 2M --shortcut-mod=lctrl --no-audio`
- **10x设备**: `-w -b 2M --shortcut-mod=lctrl --no-audio`

## 批量操作

### 选择多个设备
1. **单选**: 直接点击设备卡片
2. **多选**: 按住 `Ctrl` 键点击多个设备
3. **全选**: 菜单栏 → 设备 → 全选 (或 `Ctrl+A`)
4. **取消选择**: 菜单栏 → 设备 → 取消选择 (或 `Ctrl+D`)

### 批量运行任务
1. 选择多个设备
2. 右键任意选中的设备
3. 选择"▶️ 运行任务（多选）"
4. 配置统一的运行参数
5. 确认批量运行

### 批量显示管理
- **批量打开显示**: 右键 → "🖥️ 打开显示（多选）"
- **批量关闭显示**: 右键 → "❌ 关闭显示（多选）"

### 批量停止任务
1. 选择多个正在运行的设备
2. 右键 → "⏹️ 停止任务（多选）"

## 日志查看

### 日志标签页
- **程序日志**: 显示程序运行的总体日志
- **设备日志**: 每个运行任务的设备都有独立的日志标签页

### 日志操作
- **切换标签页**: 点击标签页名称
- **关闭设备日志**: 双击设备日志标签页（不能关闭程序日志）
- **自动滚动**: 日志会自动滚动到最新内容

### 日志内容
日志包含以下信息：
- 时间戳
- 操作类型
- 详细信息
- 错误信息（如有）

## 设置配置

### 程序设置
菜单栏 → 设置 → ⚙️ 程序设置

**当前可配置项**:
- ADB刷新间隔
- 自动连接设备
- 最小化到系统托盘
- 默认窗口大小
- 设备卡片列数

### 配置文件管理
**导出配置**: 菜单栏 → 文件 → 导出配置
**导入配置**: 菜单栏 → 文件 → 导入配置

配置文件格式为JSON，包含所有设备信息。

## 常见问题

### Q: 设备显示"未连接"状态
**A**: 
1. 确认设备已启用USB调试
2. 检查ADB连接：`adb devices`
3. 点击"🔄 刷新ADB"按钮
4. 检查设备IP地址是否正确

### Q: scrcpy显示窗口无法打开
**A**:
1. 确认scrcpy路径配置正确
2. 检查设备ADB连接状态
3. 确认设备支持屏幕投射
4. 查看设备日志了解详细错误

### Q: 任务执行失败
**A**:
1. 检查 `do_job.py` 文件是否存在
2. 确认Python环境配置正确
3. 查看设备日志了解具体错误
4. 检查设备连接状态

### Q: 程序运行缓慢
**A**:
1. 减少ADB刷新频率
2. 关闭不必要的设备日志标签页
3. 减少同时运行的任务数量
4. 检查系统资源使用情况

### Q: 窗口排列功能不工作
**A**:
1. 确认在Windows系统上运行
2. 检查是否有scrcpy窗口打开
3. 尝试手动关闭并重新打开显示窗口

## 快捷键参考

| 快捷键 | 功能 |
|--------|------|
| `Ctrl+N` | 添加新设备 |
| `Ctrl+F` | 搜索设备信息 |
| `Ctrl+A` | 全选设备 |
| `Ctrl+D` | 取消选择 |
| `Ctrl+Q` | 退出程序 |
| `Ctrl+,` | 打开设置 |

## 技巧和建议

### 设备命名建议
- 使用有意义的名称，如"测试机1"、"开发机A"
- 包含设备型号信息，如"小米10x-001"
- 避免使用特殊字符

### 性能优化建议
- 不要同时运行过多任务
- 定期关闭不需要的设备日志标签页
- 适当调整ADB刷新间隔

### 安全注意事项
- 确保设备在安全的网络环境中
- 定期备份设备配置文件
- 注意保护设备连接信息

## 故障排除

### 程序无法启动
1. 检查Python版本（需要3.8+）
2. 确认PyQt6已正确安装
3. 检查是否有权限问题
4. 查看控制台错误信息

### 设备连接问题
1. **USB连接**:
   - 确认USB调试已启用
   - 尝试重新连接USB线
   - 检查驱动程序

2. **网络连接**:
   - 确认设备和电脑在同一网络
   - 检查防火墙设置
   - 验证IP地址和端口

### 性能问题
1. **内存使用过高**:
   - 关闭不必要的日志标签页
   - 减少同时运行的任务
   - 重启程序

2. **界面响应缓慢**:
   - 减少设备数量
   - 调整刷新间隔
   - 检查系统资源

## 更新日志

### v1.0.0 (2024-12)
- ✅ 初始版本发布
- ✅ 基础设备管理功能
- ✅ ADB连接管理
- ✅ scrcpy显示集成
- ✅ 批量操作支持
- ✅ 日志系统

---

**手册版本**: 1.0
**适用程序版本**: 1.0.0
**最后更新**: 2024年12月
