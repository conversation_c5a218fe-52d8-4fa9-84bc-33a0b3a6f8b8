
from device_utils import connect_device
from task_operations_tianmao import do_job

def main():
    # 设备连接配置
    device_config = {
        "sb_name": "k80pro",
        "sb_url": "192.168.123.39:36245"
        # ; "sb_url_params": "clone"  # 可选参数，用于区分不同实例
    }

    # 初始化连接
    try:
        connect_device(**device_config)
        # 执行任务
        do_job(
            need_open_app=False,
            is_clone=True #手动打开app的话这个无用
        )
    except Exception as e:
        print(f"任务执行失败: {str(e)}")

if __name__ == "__main__":
    main()