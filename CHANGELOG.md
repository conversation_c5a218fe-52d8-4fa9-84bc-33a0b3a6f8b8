# 📝 变更日志

所有重要的项目变更都会记录在此文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- ✅ 设备拖拽排序功能
  - 支持鼠标拖拽重新排列设备卡片
  - 智能排序逻辑（从左拖到右插入后面，从右拖到左插入前面）
  - 自动保存排序结果到配置文件
  - 智能拖拽距离检测
  - 完整的拖拽事件处理链
  - 子组件事件穿透优化

### 改进
- ✅ 窗口管理系统重构
  - 移除不可靠的窗口句柄缓存机制
  - 统一使用实时窗口检测
  - 支持程序启动前的scrcpy窗口管理
  - 简化代码架构，提高可靠性
- ✅ scrcpy进程管理优化
  - 移除 `self.scrcpy_processes` 字典
  - 统一使用实时检测机制
  - 减少约28行冗余代码
  - 提高窗口操作的兼容性

### 修复
- ✅ 并列窗口功能修复
  - 修复 `ctypes.wintypes` 导入错误
  - 确保窗口排列功能正常工作
- ✅ 设备显示关闭问题修复
  - 修复程序启动前scrcpy窗口无法关闭的问题
  - 统一窗口关闭逻辑

### 计划新增
- 设备分组功能
- 任务模板系统
- 设备性能监控
- 插件系统架构
- 主题切换功能

### 计划改进
- 优化大量设备时的性能
- 增强错误处理机制
- 改进日志系统
- 完善设置功能

### 计划修复
- 设备卡片渲染异常问题
- scrcpy窗口标题显示错误
- 内存使用优化

## [1.0.0] - 2024-12-XX

### 新增
- ✅ 基础设备管理功能
  - 添加、编辑、删除设备
  - 设备信息查看
  - 多URL支持
  - 设备备注功能
  
- ✅ ADB连接管理
  - 自动检测连接状态
  - 定时刷新机制
  - 手动刷新功能
  - 自动重连机制
  
- ✅ scrcpy显示集成
  - 设备屏幕显示
  - 窗口管理功能
  - 智能窗口排列
  - 双击置于前台
  
- ✅ 任务执行系统
  - 任务运行配置
  - 多种执行动作
  - 实时状态监控
  - 进程输出捕获
  
- ✅ 批量操作支持
  - 多设备选择
  - 批量任务运行
  - 批量显示管理
  - 批量停止功能
  
- ✅ 日志系统
  - 分标签页日志
  - 实时日志更新
  - 双击关闭标签页
  - 线程安全日志记录
  
- ✅ 用户界面
  - 响应式设备卡片
  - 状态图标显示
  - 右键菜单
  - 工具栏快捷操作
  
- ✅ 配置管理
  - JSON配置文件
  - 配置导入/导出
  - 自动保存机制
  - 旧版本兼容

- ✅ 搜索功能
  - 设备信息搜索
  - 多条件过滤
  - 实时搜索结果

### 技术特性
- ✅ PyQt6图形界面框架
- ✅ 多线程任务执行
- ✅ Windows API集成
- ✅ 异常处理机制
- ✅ 内存管理优化

### 代码质量
- ✅ 清理无用代码
- ✅ 统一代码风格
- ✅ 完善错误处理
- ✅ 优化导入结构
- ✅ 添加类型注解

## [0.9.0] - 开发版本

### 新增
- 基础项目结构
- 核心类设计
- UI布局框架

### 修复
- 初始版本bug修复
- 性能优化
- 稳定性改进

## 版本说明

### 版本号格式
使用语义化版本号：`主版本号.次版本号.修订号`

- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

### 变更类型
- **新增** (Added)：新功能
- **改进** (Changed)：现有功能的变更
- **弃用** (Deprecated)：即将移除的功能
- **移除** (Removed)：已移除的功能
- **修复** (Fixed)：bug修复
- **安全** (Security)：安全相关的修复

### 发布周期
- **主版本**：重大功能更新，不定期发布
- **次版本**：功能性更新，每月发布
- **修订版本**：bug修复，按需发布

## 贡献指南

### 提交变更
1. 在对应版本下记录变更
2. 使用正确的变更类型
3. 提供清晰的描述
4. 包含相关的Issue编号

### 变更描述格式
```markdown
- [变更类型] 简短描述 (#Issue编号)
  - 详细说明（如需要）
  - 影响范围
  - 使用示例（如适用）
```

### 示例
```markdown
### 新增
- ✅ 添加设备拖拽排序功能 (#123)
  - 支持鼠标拖拽重新排列设备卡片
  - 自动保存排序结果到配置文件
  - 兼容现有的设备选择机制

### 修复  
- 🐛 修复大量设备时界面卡顿问题 (#456)
  - 优化设备卡片渲染算法
  - 减少不必要的状态更新
  - 提升50%的渲染性能
```

## 升级指南

### 从0.x升级到1.0
1. 备份现有配置文件
2. 安装新版本依赖
3. 运行程序自动迁移配置
4. 验证功能正常

### 配置文件兼容性
- 1.0版本向下兼容0.x配置
- 自动转换旧格式数据
- 保留原始配置备份

### 注意事项
- 某些API可能有变更
- 建议查看API文档了解详情
- 插件可能需要更新

---

**维护者**: 开发团队  
**最后更新**: 2024年12月
