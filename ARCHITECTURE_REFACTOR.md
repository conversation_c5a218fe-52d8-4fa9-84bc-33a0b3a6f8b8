# 🏗️ 架构重构文档

## 概述

本文档记录了设备管理程序在2024年12月进行的重要架构重构，主要涉及窗口管理系统的简化和拖拽排序功能的实现。

## 🎯 重构目标

1. **简化架构**：移除不必要的缓存机制
2. **提高可靠性**：统一使用实时检测
3. **增强功能**：支持所有类型的scrcpy窗口
4. **改善用户体验**：添加直观的拖拽排序功能

## 📋 主要变更

### 1. 窗口管理系统重构

#### 🗑️ 移除的组件
- `self.scrcpy_windows` 字典（窗口句柄缓存）
- `self.scrcpy_processes` 字典（进程管理）
- `get_and_store_window_handle()` 方法
- `cleanup_closed_processes()` 方法
- 异步窗口句柄获取机制

#### 🔄 新的实现方式
```python
# 旧方式：缓存管理
self.scrcpy_processes[device_name] = process
self.scrcpy_windows[device_name] = hwnd

# 新方式：实时检测
def has_display_running(self, device_name: str) -> bool:
    scrcpy_windows = self.get_all_scrcpy_windows()
    return any(device_name in window['title'] for window in scrcpy_windows)
```

#### 📊 重构效果
- **代码减少**：净减少约28行代码
- **方法简化**：4个主要方法得到简化
- **机制统一**：所有窗口操作使用相同的检测方式

### 2. 拖拽排序功能实现

#### 🎨 核心特性
- **智能排序逻辑**：根据拖拽方向决定插入位置
- **事件穿透**：子组件不拦截鼠标事件
- **配置持久化**：自动保存排序结果
- **视觉反馈**：拖拽过程中的状态提示

#### 🧠 排序算法
```python
def handle_device_reorder(self, source_device: str, target_device: str):
    source_index = self.device_order.index(source_device)
    target_index = self.device_order.index(target_device)
    
    # 智能插入逻辑
    insert_after = source_index < target_index
    
    if insert_after:
        # 从左拖到右：插入到目标后面
        insert_position = target_index + 1
    else:
        # 从右拖到左：插入到目标前面
        insert_position = target_index
```

#### 🔧 技术实现
- **拖拽检测**：使用Qt标准拖拽距离阈值
- **数据传输**：通过QMimeData传输设备信息
- **信号机制**：使用信号槽解耦组件通信

## 🔍 技术细节

### 窗口检测机制

#### 实时窗口查找
```python
def get_all_scrcpy_windows(self):
    """获取所有scrcpy窗口 - 实时检测"""
    windows = []
    
    def enum_windows_proc(hwnd, lParam):
        if user32.IsWindowVisible(hwnd):
            title = self.get_window_title(hwnd)
            if 'scrcpy' in title.lower() or any(device in title for device in device_names):
                windows.append({
                    'hwnd': hwnd,
                    'title': title,
                    'pid': self.get_window_pid(hwnd)
                })
        return True
    
    user32.EnumWindows(enum_windows_proc, 0)
    return windows
```

#### 设备显示状态检查
```python
def has_display_running(self, device_name: str) -> bool:
    """检查设备是否有显示正在运行"""
    scrcpy_windows = self.get_all_scrcpy_windows()
    return any(device_name in window['title'] for window in scrcpy_windows)
```

### 拖拽事件处理

#### 鼠标事件链
```python
def mousePressEvent(self, event):
    """记录拖拽起始位置"""
    if event.button() == Qt.MouseButton.LeftButton:
        self.drag_start_position = event.position().toPoint()

def mouseMoveEvent(self, event):
    """检测拖拽距离并启动拖拽"""
    distance = (event.position().toPoint() - self.drag_start_position).manhattanLength()
    if distance >= QApplication.startDragDistance():
        self.start_drag()

def start_drag(self):
    """创建拖拽对象"""
    drag = QDrag(self)
    mime_data = QMimeData()
    mime_data.setData("application/x-device-card", device_name.encode())
    drag.setMimeData(mime_data)
    drag.exec(Qt.DropAction.MoveAction)
```

#### 事件穿透设置
```python
# 为子组件设置事件穿透
self.icon_label.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)
self.name_label.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)
self.top_widget.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)
```

## 🎯 性能优化

### 1. 减少系统调用
- **旧方式**：定期轮询进程状态
- **新方式**：按需实时检测

### 2. 内存使用优化
- **移除缓存**：不再维护窗口句柄和进程字典
- **即时释放**：检测完成后立即释放资源

### 3. 响应速度提升
- **统一接口**：所有窗口操作使用相同的检测机制
- **减少延迟**：移除异步等待机制

## 🔒 兼容性保证

### 向下兼容
- **配置文件**：自动处理旧版本配置格式
- **设备数据**：保持现有设备信息结构
- **用户操作**：保持原有的交互方式

### 跨平台支持
- **Windows优先**：充分利用Windows API
- **降级处理**：API不可用时的备用方案
- **错误处理**：完善的异常捕获机制

## 🧪 测试策略

### 功能测试
- [x] 设备显示启动/关闭
- [x] 窗口排列功能
- [x] 拖拽排序操作
- [x] 配置持久化

### 兼容性测试
- [x] 程序启动前的scrcpy窗口
- [x] 程序启动后的scrcpy窗口
- [x] 混合场景下的窗口管理

### 性能测试
- [ ] 大量设备时的响应速度
- [ ] 内存使用情况
- [ ] CPU占用率

## 📈 未来规划

### 短期目标
- [ ] 完善拖拽视觉反馈
- [ ] 添加拖拽动画效果
- [ ] 优化大量设备时的性能

### 中期目标
- [ ] 支持设备分组拖拽
- [ ] 添加拖拽撤销功能
- [ ] 实现批量拖拽操作

### 长期目标
- [ ] 跨平台拖拽支持
- [ ] 自定义拖拽行为
- [ ] 拖拽操作的宏录制

## 📚 相关文档

- [拖拽功能使用指南](DRAG_DROP_GUIDE.md)
- [故障排除指南](DRAG_TROUBLESHOOTING.md)
- [进程字典移除总结](SCRCPY_PROCESSES_REMOVAL.md)
- [变更日志](CHANGELOG.md)

---

**文档版本**: 1.0  
**最后更新**: 2024年12月  
**维护者**: 开发团队
